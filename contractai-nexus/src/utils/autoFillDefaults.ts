import { ContractType } from '../components/ContractGalaxy';

export interface UserProfile {
  name?: string;
  company?: string;
  email?: string;
  address?: string;
  phone?: string;
  title?: string;
  timezone?: string;
  currency?: string;
  country?: string;
}

export interface AutoFillDefaults {
  [key: string]: any;
}

// Common default values that apply across contract types
const commonDefaults = {
  // Dates
  effectiveDate: () => new Date().toISOString().split('T')[0],
  startDate: () => new Date().toISOString().split('T')[0],
  endDate: () => {
    const date = new Date();
    date.setFullYear(date.getFullYear() + 1);
    return date.toISOString().split('T')[0];
  },
  
  // Geographic and legal
  jurisdiction: 'United States',
  governingLaw: 'State of California',
  currency: 'USD',
  timezone: 'Pacific Time (PT)',
  
  // Common business terms
  paymentTerms: 'Net 30 days',
  terminationNotice: '30 days written notice',
  disputeResolution: 'Binding arbitration',
  
  // Communication
  notificationMethod: 'Email',
  businessDays: 'Monday through Friday, excluding federal holidays',
  
  // Standard clauses
  forceMajeure: true,
  severability: true,
  entireAgreement: true,
  modification: 'Written agreement signed by both parties',
};

// Contract-specific defaults
const contractSpecificDefaults: Record<string, AutoFillDefaults> = {
  nda: {
    confidentialityPeriod: '2 years',
    returnOfMaterials: true,
    mutualDisclosure: false,
    disclosurePurpose: 'Business discussions and potential collaboration',
    remedies: 'Injunctive relief and monetary damages',
    survivingClauses: 'Confidentiality obligations survive termination',
  },
  
  employment: {
    employmentType: 'Full-time',
    workLocation: 'Hybrid (Office/Remote)',
    probationPeriod: '90 days',
    vacationDays: '15 days per year',
    sickDays: '10 days per year',
    benefitsEligible: true,
    intellectualProperty: 'Work for hire - all rights to employer',
    nonCompetePeriod: '6 months',
    workHours: '40 hours per week',
  },
  
  service: {
    serviceType: 'Professional services',
    paymentSchedule: 'Monthly',
    autoRenewal: true,
    renewalPeriod: '12 months',
    serviceLevel: 'Standard business hours support',
    liability: 'Limited to contract value',
    dataRetention: '7 years',
    backupFrequency: 'Daily',
  },
  
  lease: {
    leaseType: 'Fixed-term',
    leaseDuration: '12 months',
    paymentFrequency: 'Monthly',
    securityDeposit: 'One month rent',
    petPolicy: 'No pets allowed',
    smokingPolicy: 'No smoking',
    maintenanceResponsibility: 'Landlord for major repairs, tenant for minor',
    renewalOption: true,
    utilitiesIncluded: false,
  },
  
  partnership: {
    partnershipType: 'General Partnership',
    profitSharingRatio: '50/50',
    decisionMaking: 'Unanimous consent for major decisions',
    capitalContribution: 'Equal contributions',
    withdrawalNotice: '90 days',
    meetingFrequency: 'Monthly',
    bookkeepingMethod: 'Accrual basis',
    fiscalYearEnd: 'December 31',
  },
  
  custom: {
    contractType: 'Custom Agreement',
    performanceStandard: 'Industry best practices',
    complianceRequirements: 'Applicable laws and regulations',
    reportingFrequency: 'Quarterly',
    auditRights: 'Annual audit permitted',
  },
};

/**
 * Generate auto-fill defaults for a specific contract type
 */
export const getAutoFillDefaults = (
  contractType: ContractType | string,
  userProfile?: UserProfile,
  templateDefaults?: AutoFillDefaults
): AutoFillDefaults => {
  const defaults: AutoFillDefaults = {};
  
  // Start with common defaults
  Object.entries(commonDefaults).forEach(([key, value]) => {
    defaults[key] = typeof value === 'function' ? value() : value;
  });
  
  // Get contract type ID
  const contractTypeId = typeof contractType === 'string' ? contractType : contractType.id;
  
  // Add contract-specific defaults
  const contractDefaults = contractSpecificDefaults[contractTypeId] || {};
  Object.assign(defaults, contractDefaults);
  
  // Add template defaults if provided (highest priority)
  if (templateDefaults) {
    Object.assign(defaults, templateDefaults);
  }
  
  // Add user profile data if available
  if (userProfile) {
    if (userProfile.name) defaults.userName = userProfile.name;
    if (userProfile.company) defaults.companyName = userProfile.company;
    if (userProfile.email) defaults.userEmail = userProfile.email;
    if (userProfile.address) defaults.userAddress = userProfile.address;
    if (userProfile.phone) defaults.userPhone = userProfile.phone;
    if (userProfile.title) defaults.userTitle = userProfile.title;
    if (userProfile.currency) defaults.currency = userProfile.currency;
    if (userProfile.country) defaults.jurisdiction = userProfile.country;
  }
  
  return defaults;
};

/**
 * Get smart suggestions for field values based on field name and context
 */
export const getFieldSuggestions = (fieldName: string, contractType: ContractType | string): string[] => {
  const suggestionMap: Record<string, string[]> = {
    // Payment terms
    paymentTerms: ['Net 30 days', 'Net 15 days', 'Due on receipt', 'Net 60 days', '50% upfront, 50% on completion'],
    paymentSchedule: ['Monthly', 'Quarterly', 'Annually', 'Per milestone', 'Weekly'],
    
    // Duration and timing
    contractDuration: ['12 months', '24 months', '6 months', '3 years', 'Indefinite'],
    terminationNotice: ['30 days', '60 days', '90 days', '14 days', '7 days'],
    renewalPeriod: ['12 months', '24 months', '6 months', 'Month-to-month'],
    
    // Work arrangements
    workLocation: ['Remote', 'On-site', 'Hybrid', 'Client location', 'Flexible'],
    workHours: ['40 hours per week', '20 hours per week', 'Flexible', 'Business hours only'],
    
    // Legal and compliance
    governingLaw: ['State of California', 'State of New York', 'State of Texas', 'State of Delaware'],
    disputeResolution: ['Binding arbitration', 'Mediation then arbitration', 'Court litigation', 'Mediation only'],
    
    // Confidentiality
    confidentialityPeriod: ['2 years', '3 years', '5 years', 'Indefinite', '1 year'],
    
    // Service levels
    serviceLevel: ['24/7 support', 'Business hours only', 'Best effort', 'Guaranteed response time'],
  };
  
  return suggestionMap[fieldName] || [];
};

/**
 * Validate that required fields have values
 */
export const validateRequiredFields = (
  data: Record<string, any>, 
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } => {
  const missingFields = requiredFields.filter(field => {
    const value = data[field];
    return !value || (typeof value === 'string' && value.trim() === '');
  });
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
  };
};

/**
 * Get user-friendly field labels
 */
export const getFieldLabel = (fieldName: string): string => {
  const labelMap: Record<string, string> = {
    disclosingParty: 'Disclosing Party Name',
    receivingParty: 'Receiving Party Name',
    effectiveDate: 'Effective Date',
    contractorName: 'Contractor Name',
    clientName: 'Client Name',
    projectDescription: 'Project Description',
    compensation: 'Compensation Amount',
    deadline: 'Project Deadline',
    serviceProvider: 'Service Provider Name',
    client: 'Client Name',
    serviceDescription: 'Service Description',
    monthlyFee: 'Monthly Fee',
    startDate: 'Start Date',
    landlord: 'Landlord Name',
    tenant: 'Tenant Name',
    propertyAddress: 'Property Address',
    monthlyRent: 'Monthly Rent',
    partner1Name: 'First Partner Name',
    partner2Name: 'Second Partner Name',
    businessPurpose: 'Business Purpose',
    initialCapital: 'Initial Capital Investment',
    consultant: 'Consultant Name',
    scopeOfWork: 'Scope of Work',
    hourlyRate: 'Hourly Rate',
  };
  
  return labelMap[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
};
