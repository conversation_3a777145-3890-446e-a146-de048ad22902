import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ExpressModeContextType {
  isExpressMode: boolean;
  setIsExpressMode: (value: boolean) => void;
  toggleExpressMode: () => void;
}

const ExpressModeContext = createContext<ExpressModeContextType | undefined>(undefined);

export const useExpressMode = () => {
  const context = useContext(ExpressModeContext);
  if (context === undefined) {
    throw new Error('useExpressMode must be used within an ExpressModeProvider');
  }
  return context;
};

interface ExpressModeProviderProps {
  children: ReactNode;
}

export const ExpressModeProvider: React.FC<ExpressModeProviderProps> = ({ children }) => {
  const [isExpressMode, setIsExpressMode] = useState(true); // Default to express mode for simplicity

  const toggleExpressMode = () => {
    setIsExpressMode(prev => !prev);
  };

  return (
    <ExpressModeContext.Provider value={{
      isExpressMode,
      setIsExpressMode,
      toggleExpressMode,
    }}>
      {children}
    </ExpressModeContext.Provider>
  );
};
