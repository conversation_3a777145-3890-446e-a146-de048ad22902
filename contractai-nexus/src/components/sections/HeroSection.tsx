import React from 'react';
import { motion } from 'framer-motion';

interface HeroSectionProps {
  onStartClick: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onStartClick }) => {
  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-dark-100 dark:via-dark-150 dark:to-dark-200">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Primary gradient orbs */}
        <div className="absolute -top-40 -right-40 w-[600px] h-[600px] bg-gradient-to-br from-primary-400/30 to-primary-600/20 dark:from-primary-800/20 dark:to-primary-900/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '4s' }} />
        <div className="absolute top-1/4 -left-40 w-[500px] h-[500px] bg-gradient-to-br from-secondary-400/20 to-secondary-600/15 dark:from-secondary-800/15 dark:to-secondary-900/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '6s', animationDelay: '1s' }} />
        <div className="absolute bottom-0 right-1/4 w-[400px] h-[400px] bg-gradient-to-br from-accent-400/25 to-accent-600/15 dark:from-accent-800/15 dark:to-accent-900/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '2s' }} />
        
        {/* Hero Section */}
        <motion.div 
          className="relative min-h-screen flex items-center justify-center overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          {/* Background Elements */}
          <div className="absolute inset-0">
            {/* Gradient background */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-dark-100 dark:via-dark-50 dark:to-dark-100" />
            
            {/* Animated gradient orbs */}
            <motion.div 
              className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-primary-400/30 to-secondary-400/30 rounded-full blur-3xl"
              animate={{ 
                x: [0, 30, 0],
                y: [0, -20, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div 
              className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-secondary-400/20 to-primary-400/20 rounded-full blur-3xl"
              animate={{ 
                x: [0, -40, 0],
                y: [0, 30, 0],
                scale: [1, 0.9, 1]
              }}
              transition={{ 
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            />
            
            {/* Grid pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTVlN2ViIiBzdHJva2Utd2lkdGg9IjEiIG9wYWNpdHk9IjAuMyIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmlkKSIvPjwvc3ZnPg==')] opacity-20" />
            
            {/* Floating particles */}
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-primary-400/40 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -30, 0],
                  opacity: [0.4, 0.8, 0.4],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>
          
          {/* Main Content Container */}
          <div className="container mx-auto px-6 lg:px-8 relative z-10">
            {/* Trust badge */}
            <motion.div 
              className="flex justify-center mb-8"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="inline-flex items-center px-4 py-2 bg-white/80 dark:bg-dark-200/80 backdrop-blur-sm rounded-full shadow-lg border border-white/20 dark:border-dark-300/20">
                <span className="text-sm font-medium text-primary-600 dark:text-primary-400 mr-2">✨</span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Trusted by 10,000+ professionals</span>
              </div>
            </motion.div>
            
            {/* Centered Hero Content */}
            <div className="text-center mb-16">
              <motion.h1 
                className="text-5xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-primary-600 via-purple-600 to-secondary-600 bg-clip-text text-transparent leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Contract AI
                <span className="block text-4xl lg:text-6xl mt-2">Nexus</span>
                <motion.span 
                  className="block text-2xl lg:text-3xl mt-4 bg-gradient-to-r from-secondary-500 to-primary-500 bg-clip-text text-transparent font-medium"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  Reimagined
                </motion.span>
              </motion.h1>
              
              <motion.p 
                className="text-xl lg:text-2xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-4xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.9 }}
              >
                Transform legal document creation with AI-powered intelligence. 
                Generate, review, and manage contracts with unprecedented speed and accuracy.
              </motion.p>
              
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 mb-12 justify-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.1 }}
              >
                <button 
                  onClick={onStartClick}
                  className="group relative px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
                >
                  <span className="relative z-10">Start Free Trial</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-700 to-primary-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </button>
                <button className="px-8 py-4 border-2 border-gray-300 dark:border-dark-300 text-gray-700 dark:text-gray-300 rounded-xl font-semibold text-lg hover:border-primary-500 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-300">
                  Watch Demo
                </button>
              </motion.div>
            </div>
            
            {/* Centered Interactive Contract Document Preview */}
            <motion.div 
              className="flex justify-center mb-16 relative"
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{ duration: 1, delay: 1.3, ease: "easeOut" }}
            >
              <div className="relative w-full max-w-5xl">
                {/* Background glow */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 via-purple-500/15 to-secondary-500/20 rounded-3xl blur-3xl" />
                
                {/* Main document container */}
                <div className="relative bg-white dark:bg-dark-200 rounded-3xl shadow-2xl border border-gray-200/50 dark:border-dark-300/50 overflow-hidden">
                  {/* Document header */}
                  <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-dark-300">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-red-500 rounded-full" />
                      <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                      <div className="w-3 h-3 bg-green-500 rounded-full" />
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">ContractAI Nexus</div>
                  </div>
                  
                  {/* Document content */}
                  <div className="p-10 space-y-8">
                    {/* Document title */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: 0.8 }}
                    >
                      <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">Service Agreement</h3>
                      <div className="flex items-center space-x-4 text-base text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                          AI Generated
                        </span>
                        <span>•</span>
                        <span>Legal Compliant</span>
                        <span>•</span>
                        <span>Ready to Sign</span>
                      </div>
                    </motion.div>
                    
                    {/* Contract clauses with typing animation */}
                    <div className="space-y-6">
                      {[
                        { text: "This Service Agreement is entered between the parties...", delay: 1.2 },
                        { text: "Scope of Work: The service provider agrees to...", delay: 1.8 },
                        { text: "Payment Terms: Compensation shall be...", delay: 2.4 },
                        { text: "Confidentiality: Both parties acknowledge...", delay: 3.0 }
                      ].map((clause, index) => (
                        <motion.div
                          key={index}
                          className="flex items-start space-x-3"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.6, delay: clause.delay }}
                        >
                          <div className="w-6 h-6 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <span className="text-primary-600 dark:text-primary-400 text-xs font-bold">{index + 1}</span>
                          </div>
                          <div className="flex-1">
                            <motion.p 
                              className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 1, delay: clause.delay + 0.3 }}
                            >
                              {clause.text}
                            </motion.p>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Signature section */}
                    <motion.div
                      className="border-t border-gray-200 dark:border-dark-300 pt-8 mt-10"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: 3.5 }}
                    >
                      <div className="grid grid-cols-2 gap-8">
                        <div className="space-y-3">
                          <div className="text-base text-gray-500 dark:text-gray-400 font-medium">Client Signature</div>
                          <div className="h-16 border-2 border-dashed border-gray-300 dark:border-dark-400 rounded-xl flex items-center justify-center">
                            <motion.div
                              className="text-primary-600 dark:text-primary-400 font-signature text-2xl"
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ duration: 0.8, delay: 4 }}
                            >
                              John Smith
                            </motion.div>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="text-base text-gray-500 dark:text-gray-400 font-medium">Service Provider</div>
                          <div className="h-16 border-2 border-dashed border-gray-300 dark:border-dark-400 rounded-xl flex items-center justify-center">
                            <motion.div
                              className="text-secondary-600 dark:text-secondary-400 font-signature text-2xl"
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ duration: 0.8, delay: 4.3 }}
                            >
                              Sarah Johnson
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                  
                  {/* Progress indicator */}
                  <motion.div 
                    className="absolute bottom-0 left-0 h-2 bg-gradient-to-r from-primary-500 to-secondary-500"
                    initial={{ width: "0%" }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 4, delay: 1 }}
                  />
                </div>
                
                {/* Floating feature badges */}
                <motion.div 
                  className="absolute -top-4 -right-8 glass-panel-hover px-3 py-2 text-xs shadow-lg backdrop-blur-md z-20"
                  animate={{ 
                    y: [-4, 4, -4],
                    rotate: [-1, 1, -1]
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity, 
                    ease: "easeInOut"
                  }}
                >
                  <div className="text-green-600 dark:text-green-400 font-semibold">✓ AI Generated</div>
                </motion.div>
                
                <motion.div 
                  className="absolute -bottom-4 -left-8 glass-panel-hover px-3 py-2 text-xs shadow-lg backdrop-blur-md z-20"
                  animate={{ 
                    y: [4, -4, 4],
                    rotate: [1, -1, 1]
                  }}
                  transition={{ 
                    duration: 4, 
                    repeat: Infinity, 
                    ease: "easeInOut",
                    delay: 2
                  }}
                >
                  <div className="text-blue-600 dark:text-blue-400 font-semibold">⚡ Instant Setup</div>
                </motion.div>
              </div>
            </motion.div>
            
            {/* Trust badges centered below */}
            <motion.div 
              className="flex flex-wrap gap-3 justify-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.5 }}
            >
              {['SOC 2 Compliant', 'GDPR Ready', 'Enterprise Security', '99.9% Uptime'].map((feature) => (
                <span 
                  key={feature}
                  className="flex items-center px-3 py-1 bg-white/60 dark:bg-dark-200/60 backdrop-blur-sm rounded-full text-gray-700 dark:text-gray-300 border border-white/20 dark:border-dark-300/20"
                >
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                  {feature}
                </span>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
