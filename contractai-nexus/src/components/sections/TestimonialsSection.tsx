import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const TestimonialsSection: React.FC = () => {
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Legal Director",
      company: "TechCorp Inc.",
      avatar: "👩‍💼",
      rating: 5,
      quote: "ContractAI Nexus has revolutionized our legal workflow. What used to take weeks now takes hours. The AI-generated contracts are incredibly accurate and legally sound.",
      highlight: "Reduced contract creation time by 90%"
    },
    {
      name: "<PERSON>",
      role: "Startup Founder",
      company: "InnovateLab",
      avatar: "👨‍💻",
      rating: 5,
      quote: "As a startup, we couldn't afford expensive legal fees. ContractAI Nexus gave us enterprise-level contract management at a fraction of the cost.",
      highlight: "Saved $50K+ in legal fees"
    },
    {
      name: "<PERSON>",
      role: "Operations Manager",
      company: "Global Solutions Ltd.",
      avatar: "👩‍💼",
      rating: 5,
      quote: "The collaboration features are outstanding. Our team can now work on contracts simultaneously, and the version control keeps everything organized.",
      highlight: "Improved team collaboration by 300%"
    },
    {
      name: "<PERSON>",
      role: "General Counsel",
      company: "Enterprise Corp",
      avatar: "👨‍💼",
      rating: 5,
      quote: "The security and compliance features give us complete confidence. SOC 2 compliance and enterprise-grade encryption are exactly what we needed.",
      highlight: "100% compliance maintained"
    }
  ];

  const trustMetrics = [
    { label: "Average Rating", value: "4.9/5", icon: "⭐" },
    { label: "Happy Users", value: "10,000+", icon: "😊" },
    { label: "Uptime", value: "99.9%", icon: "🚀" },
    { label: "Support Response", value: "<2hrs", icon: "💬" }
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <section className="py-24 bg-white dark:bg-dark-100 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-primary-200/15 to-secondary-200/15 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-secondary-200/10 to-accent-200/10 rounded-full blur-3xl" />
        
        {/* Decorative elements */}
        <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-primary-400 rounded-full animate-pulse" />
        <div className="absolute bottom-1/3 left-1/3 w-3 h-3 bg-secondary-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/4 w-1 h-1 bg-accent-400 rounded-full animate-pulse" style={{ animationDelay: '2s' }} />
      </div>

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.span
            className="inline-block px-4 py-2 bg-accent-100 dark:bg-accent-900/30 text-accent-600 dark:text-accent-400 rounded-full text-sm font-semibold mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            💬 What Our Users Say
          </motion.span>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Trusted by professionals
            <span className="block bg-gradient-to-r from-accent-600 to-primary-600 bg-clip-text text-transparent">
              worldwide
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Join thousands of legal professionals, startups, and enterprises who have 
            transformed their contract workflows with ContractAI Nexus.
          </p>
        </motion.div>

        {/* Featured Testimonial */}
        <div className="max-w-4xl mx-auto mb-16">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTestimonial}
              className="bg-gradient-to-br from-white to-gray-50 dark:from-dark-200 dark:to-dark-250 rounded-3xl p-8 lg:p-12 shadow-2xl border border-gray-200/50 dark:border-dark-300/50 relative overflow-hidden"
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -30, scale: 0.95 }}
              transition={{ duration: 0.6 }}
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-200/20 to-secondary-200/20 rounded-full blur-2xl" />
              
              {/* Quote icon */}
              <div className="absolute top-8 left-8 text-6xl text-primary-200 dark:text-primary-800 font-serif">"</div>
              
              {/* Content */}
              <div className="relative z-10">
                {/* Stars */}
                <div className="flex items-center justify-center mb-6">
                  {[...Array(testimonials[activeTestimonial].rating)].map((_, i) => (
                    <motion.span
                      key={i}
                      className="text-2xl text-yellow-400"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: i * 0.1 }}
                    >
                      ⭐
                    </motion.span>
                  ))}
                </div>
                
                {/* Quote */}
                <blockquote className="text-xl lg:text-2xl text-gray-700 dark:text-gray-300 text-center leading-relaxed mb-8 font-medium">
                  {testimonials[activeTestimonial].quote}
                </blockquote>
                
                {/* Highlight */}
                <div className="text-center mb-8">
                  <span className="inline-block px-4 py-2 bg-gradient-to-r from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-semibold">
                    🎯 {testimonials[activeTestimonial].highlight}
                  </span>
                </div>
                
                {/* Author */}
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-2xl shadow-lg">
                    {testimonials[activeTestimonial].avatar}
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-gray-900 dark:text-white text-lg">
                      {testimonials[activeTestimonial].name}
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">
                      {testimonials[activeTestimonial].role}
                    </div>
                    <div className="text-primary-600 dark:text-primary-400 font-semibold">
                      {testimonials[activeTestimonial].company}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Testimonial Navigation */}
        <div className="flex justify-center space-x-4 mb-16">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveTestimonial(index)}
              className={`w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                activeTestimonial === index
                  ? 'border-primary-500 bg-primary-500 text-white scale-110'
                  : 'border-gray-300 dark:border-dark-400 bg-white dark:bg-dark-200 text-gray-600 dark:text-gray-400 hover:border-primary-300 hover:scale-105'
              }`}
            >
              {testimonials[index].avatar}
            </button>
          ))}
        </div>

        {/* Trust Metrics */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {trustMetrics.map((metric, index) => (
            <motion.div
              key={index}
              className="text-center p-6 bg-white dark:bg-dark-200 rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-300/50 hover:shadow-xl transition-shadow duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <div className="text-3xl mb-3">{metric.icon}</div>
              <div className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {metric.value}
              </div>
              <div className="text-gray-600 dark:text-gray-400 font-medium">
                {metric.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
            Join the growing community of satisfied users
          </p>
          <button className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-accent-600 to-primary-600 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
            <span>Start Your Success Story</span>
            <motion.span
              className="ml-2"
              animate={{ x: [0, 4, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              →
            </motion.span>
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
