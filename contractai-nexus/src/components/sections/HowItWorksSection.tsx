import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const HowItWorksSection: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      number: "01",
      title: "Define Requirements",
      description: "Tell us what type of contract you need and provide key details about parties, terms, and specific requirements.",
      details: "Our AI analyzes your input and suggests the most appropriate contract structure, clauses, and legal frameworks based on your industry and jurisdiction.",
      icon: "📝",
      color: "from-blue-500 to-cyan-500"
    },
    {
      number: "02", 
      title: "AI Generation",
      description: "Our advanced AI creates a comprehensive, legally compliant contract draft tailored to your specifications.",
      details: "Using cutting-edge language models trained on millions of legal documents, we generate contracts that meet industry standards and regulatory requirements.",
      icon: "🤖",
      color: "from-purple-500 to-pink-500"
    },
    {
      number: "03",
      title: "Review & Refine",
      description: "Review the generated contract, make edits, and collaborate with stakeholders using our intuitive editing tools.",
      details: "Real-time collaboration features allow multiple parties to review, comment, and suggest changes with full version control and audit trails.",
      icon: "🔍",
      color: "from-amber-500 to-orange-500"
    },
    {
      number: "04",
      title: "Sign & Execute",
      description: "Finalize your contract with secure digital signatures and automated distribution to all parties.",
      details: "Legally binding e-signatures with identity verification, tamper-proof sealing, and automatic storage in your secure document vault.",
      icon: "✍️",
      color: "from-green-500 to-emerald-500"
    }
  ];

  return (
    <section className="py-24 bg-gray-50 dark:bg-dark-150 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 -left-20 w-96 h-96 bg-gradient-to-r from-primary-200/10 to-secondary-200/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 -right-20 w-96 h-96 bg-gradient-to-r from-secondary-200/10 to-accent-200/10 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.span
            className="inline-block px-4 py-2 bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-400 rounded-full text-sm font-semibold mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            🚀 How It Works
          </motion.span>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            From idea to signed contract
            <span className="block bg-gradient-to-r from-secondary-600 to-primary-600 bg-clip-text text-transparent">
              in four simple steps
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Our streamlined process transforms complex legal document creation into 
            a simple, intuitive workflow that anyone can master.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Interactive Timeline */}
          <div className="space-y-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                className={`relative cursor-pointer transition-all duration-300 ${
                  activeStep === index ? 'scale-105' : 'hover:scale-102'
                }`}
                onClick={() => setActiveStep(index)}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="absolute left-8 top-20 w-0.5 h-16 bg-gradient-to-b from-gray-300 to-transparent dark:from-dark-300" />
                )}
                
                {/* Step Card */}
                <div className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
                  activeStep === index
                    ? 'bg-white dark:bg-dark-200 border-primary-300 dark:border-primary-600 shadow-xl'
                    : 'bg-white/50 dark:bg-dark-200/50 border-gray-200 dark:border-dark-300 hover:border-primary-200 dark:hover:border-primary-700'
                }`}>
                  <div className="flex items-start space-x-4">
                    {/* Step Number & Icon */}
                    <div className={`flex-shrink-0 w-16 h-16 rounded-2xl bg-gradient-to-br ${step.color} flex items-center justify-center text-white font-bold text-lg shadow-lg ${
                      activeStep === index ? 'scale-110' : ''
                    } transition-transform duration-300`}>
                      <span className="text-2xl">{step.icon}</span>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className={`text-sm font-bold px-2 py-1 rounded-full bg-gradient-to-r ${step.color} text-white`}>
                          {step.number}
                        </span>
                        <h3 className={`text-xl font-bold transition-colors duration-300 ${
                          activeStep === index
                            ? 'text-primary-600 dark:text-primary-400'
                            : 'text-gray-900 dark:text-white'
                        }`}>
                          {step.title}
                        </h3>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {step.description}
                      </p>
                      
                      {/* Active indicator */}
                      <motion.div
                        className={`mt-3 flex items-center text-primary-600 dark:text-primary-400 font-semibold transition-opacity duration-300 ${
                          activeStep === index ? 'opacity-100' : 'opacity-0'
                        }`}
                        animate={{ x: activeStep === index ? [0, 4, 0] : 0 }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        Click to learn more →
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Active Step Details */}
          <div className="lg:sticky lg:top-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeStep}
                className="bg-white dark:bg-dark-200 rounded-3xl p-8 shadow-2xl border border-gray-200/50 dark:border-dark-300/50"
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{ duration: 0.4 }}
              >
                {/* Header */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className={`w-20 h-20 rounded-3xl bg-gradient-to-br ${steps[activeStep].color} flex items-center justify-center text-3xl shadow-lg`}>
                    {steps[activeStep].icon}
                  </div>
                  <div>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-bold bg-gradient-to-r ${steps[activeStep].color} text-white mb-2`}>
                      Step {steps[activeStep].number}
                    </span>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {steps[activeStep].title}
                    </h3>
                  </div>
                </div>
                
                {/* Description */}
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                  {steps[activeStep].description}
                </p>
                
                {/* Detailed explanation */}
                <div className="bg-gray-50 dark:bg-dark-300 rounded-2xl p-6">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                    How it works:
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {steps[activeStep].details}
                  </p>
                </div>
                
                {/* Progress indicator */}
                <div className="mt-6">
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
                    <span>Progress</span>
                    <span>{Math.round(((activeStep + 1) / steps.length) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-dark-400 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full bg-gradient-to-r ${steps[activeStep].color}`}
                      initial={{ width: "0%" }}
                      animate={{ width: `${((activeStep + 1) / steps.length) * 100}%` }}
                      transition={{ duration: 0.6, ease: "easeOut" }}
                    />
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
            Ready to streamline your contract workflow?
          </p>
          <button className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-secondary-600 to-primary-600 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
            <span>Get Started Now</span>
            <motion.span
              className="ml-2"
              animate={{ x: [0, 4, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              →
            </motion.span>
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
