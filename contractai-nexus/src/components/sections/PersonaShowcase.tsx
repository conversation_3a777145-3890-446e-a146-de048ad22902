import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Rocket, 
  Palette, 
  Scale, 
  Users,
  ArrowRight,
  Clock,
  Shield,
  Zap
} from 'lucide-react';

interface Persona {
  id: string;
  name: string;
  role: string;
  company: string;
  icon: React.ReactNode;
  needs: string[];
  painPoints: string[];
  goals: string;
  primaryUseCase: string;
  timeToValue: string;
  bgGradient: string;
  iconBg: string;
}

const personas: Persona[] = [
  {
    id: 'sarah',
    name: '<PERSON>',
    role: 'Startup Founder',
    company: 'TechStart Inc.',
    icon: <Rocket className="w-6 h-6" />,
    needs: ['Quick contracts for hiring', 'Partnership agreements', 'Fundraising documents'],
    painPoints: ['Limited legal budget', 'Need to move fast', 'Worried about missing critical clauses'],
    goals: 'Create professional contracts quickly without expensive legal counsel for every draft',
    primaryUseCase: 'Employment & Partnership Contracts',
    timeToValue: '2 minutes',
    bgGradient: 'from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20',
    iconBg: 'bg-blue-500'
  },
  {
    id: 'marcus',
    name: '<PERSON>',
    role: 'Freelance Designer',
    company: 'Creative Studio',
    icon: <Palette className="w-6 h-6" />,
    needs: ['Client agreements', 'IP protection clauses', 'Payment terms'],
    painPoints: ['Reuses old contracts', 'Unsure if terms are optimal', 'Not protective enough'],
    goals: 'Standardize client onboarding with professional contracts that don\'t require legal review each time',
    primaryUseCase: 'Service & Freelancer Agreements',
    timeToValue: '3 minutes',
    bgGradient: 'from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20',
    iconBg: 'bg-purple-500'
  },
  {
    id: 'elena',
    name: 'Elena',
    role: 'Corporate Lawyer',
    company: 'LegalCorp Partners',
    icon: <Scale className="w-6 h-6" />,
    needs: ['Efficient contract creation', 'High volume agreement review', 'Quality control'],
    painPoints: ['Repetitive work', 'Similar contracts', 'Explaining terms to non-legal stakeholders'],
    goals: 'Automate routine contract creation while maintaining quality control and compliance',
    primaryUseCase: 'Enterprise Contract Automation',
    timeToValue: '5 minutes',
    bgGradient: 'from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20',
    iconBg: 'bg-emerald-500'
  },
  {
    id: 'david',
    name: 'David',
    role: 'HR Director',
    company: 'GlobalTech Corp',
    icon: <Users className="w-6 h-6" />,
    needs: ['Compliant employment agreements', 'Multi-jurisdiction contracts', 'Role-specific terms'],
    painPoints: ['Changing employment laws', 'Ensuring consistency', 'Adapting to locations'],
    goals: 'Standardize employment documents while adapting to specific roles and locations',
    primaryUseCase: 'Employment & HR Documents',
    timeToValue: '4 minutes',
    bgGradient: 'from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20',
    iconBg: 'bg-amber-500'
  }
];

const PersonaCard: React.FC<{ persona: Persona; index: number; onGetStarted: () => void }> = ({ 
  persona, 
  index, 
  onGetStarted 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className={`relative bg-gradient-to-br ${persona.bgGradient} rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50 group`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`${persona.iconBg} text-white p-2 rounded-lg`}>
            {persona.icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {persona.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {persona.role}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {persona.company}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
          <Clock className="w-3 h-3" />
          <span>{persona.timeToValue}</span>
        </div>
      </div>

      {/* Primary Use Case */}
      <div className="mb-4">
        <div className="inline-flex items-center space-x-1 bg-white/70 dark:bg-gray-800/70 px-2 py-1 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300">
          <Zap className="w-3 h-3" />
          <span>{persona.primaryUseCase}</span>
        </div>
      </div>

      {/* Goals */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          <Shield className="w-4 h-4 mr-1" />
          Goals
        </h4>
        <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
          {persona.goals}
        </p>
      </div>

      {/* Pain Points */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          Pain Points
        </h4>
        <ul className="space-y-1">
          {persona.painPoints.map((point, idx) => (
            <li key={idx} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
              <span className="w-1 h-1 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
              {point}
            </li>
          ))}
        </ul>
      </div>

      {/* Needs */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          Key Needs
        </h4>
        <ul className="space-y-1">
          {persona.needs.map((need, idx) => (
            <li key={idx} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
              <span className="w-1 h-1 bg-green-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
              {need}
            </li>
          ))}
        </ul>
      </div>

      {/* CTA Button */}
      <button
        onClick={onGetStarted}
        className="w-full bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 text-gray-900 dark:text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 group-hover:shadow-md border border-gray-200/50 dark:border-gray-700/50"
      >
        <span>Start Like {persona.name}</span>
        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
      </button>
    </motion.div>
  );
};

const PersonaShowcase: React.FC = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/create');
  };

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Built for Every Professional
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            See how ContractAI Nexus solves real challenges for professionals across different industries. 
            Find your story and start creating contracts that work for you.
          </p>
        </motion.div>

        {/* Persona Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {personas.map((persona, index) => (
            <PersonaCard
              key={persona.id}
              persona={persona}
              index={index}
              onGetStarted={handleGetStarted}
            />
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Don't see your role? ContractAI Nexus adapts to any professional's contract needs.
          </p>
          <button
            onClick={handleGetStarted}
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 inline-flex items-center space-x-2"
          >
            <span>Try It Free</span>
            <ArrowRight className="w-5 h-5" />
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default PersonaShowcase;
