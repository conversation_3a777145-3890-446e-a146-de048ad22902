import React, { useState, useRef, useEffect } from 'react';
import { Clause } from '../types/clauses';
import { ContractType } from './ContractGalaxy';

interface ContractPreviewProps {
  contractType: ContractType | null;
  formData: Record<string, any>;
  clauses: Clause[];
  className?: string;
}

interface Signature {
  partyName: string;
  signatureData: string | null;
  dateSigned: Date | null;
}

const ContractPreview: React.FC<ContractPreviewProps> = ({
  contractType,
  formData,
  clauses,
  className = '',
}) => {
  const [signatures, setSignatures] = useState<Signature[]>([]);
  const [activeSigningParty, setActiveSigningParty] = useState<string | null>(null);
  const [isLegalMode, setIsLegalMode] = useState<boolean>(true);
  const signatureCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const previewScrollRef = useRef<HTMLDivElement | null>(null);
  const lastScrollTopRef = useRef<number>(0);
  const [lastPosition, setLastPosition] = useState<{ x: number; y: number } | null>(null);
  const [copiedAnchor, setCopiedAnchor] = useState<string | null>(null);
  const copyTimeoutRef = useRef<number | null>(null);

  // Early return moved below hooks to satisfy React hook rules

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const slugify = (text: string) =>
    (text || '')
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .trim()
      .replace(/\s+/g, '-');

  // Preserve scroll position across clause reorders
  useEffect(() => {
    const el = previewScrollRef.current;
    if (!el) return;
    const onScroll = () => {
      lastScrollTopRef.current = el.scrollTop;
    };
    el.addEventListener('scroll', onScroll, { passive: true });
    return () => {
      el.removeEventListener('scroll', onScroll);
    };
  }, []);

  useEffect(() => {
    const el = previewScrollRef.current;
    if (!el) return;
    // Restore last scrollTop after clauses changed
    el.scrollTop = lastScrollTopRef.current;
  }, [clauses]);

  useEffect(() => {
    return () => {
      if (copyTimeoutRef.current) {
        window.clearTimeout(copyTimeoutRef.current);
      }
    };
  }, []);

  const getPartyNames = () => {
    const parties = [];
    if (formData.firstPartyName) {
      parties.push(formData.firstPartyName);
    }
    if (formData.secondPartyName) {
      parties.push(formData.secondPartyName);
    }
    return parties;
  };

  const handleStartSigning = (partyName: string) => {
    setActiveSigningParty(partyName);
    
    // Clear the canvas
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const handleCancelSigning = () => {
    setActiveSigningParty(null);
  };

  const handleCompleteSigning = () => {
    if (!activeSigningParty) return;
    
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const signatureData = canvas.toDataURL('image/png');
      
      setSignatures((prev) => [
        ...prev.filter((sig) => sig.partyName !== activeSigningParty),
        {
          partyName: activeSigningParty,
          signatureData,
          dateSigned: new Date(),
        },
      ]);
      
      setActiveSigningParty(null);
    }
  };

  const handleStartDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    let clientX, clientY;
    
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;
    
    setLastPosition({ x, y });
  };

  const handleDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !lastPosition) return;
    
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const rect = canvas.getBoundingClientRect();
    let clientX, clientY;
    
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
      e.preventDefault(); // Prevent scrolling on touch devices
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;
    
    ctx.beginPath();
    ctx.moveTo(lastPosition.x, lastPosition.y);
    ctx.lineTo(x, y);
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.stroke();
    ctx.closePath();
    
    setLastPosition({ x, y });
  };

  const handleEndDrawing = () => {
    setIsDrawing(false);
    setLastPosition(null);
  };

  const clearSignature = () => {
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const renderSignatureSection = () => {
    const parties = getPartyNames();
    
    return (
      <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          Signatures
        </h3>
        
        {activeSigningParty ? (
          <div className="space-y-4">
            <p className="text-gray-700 dark:text-gray-300">
              {activeSigningParty}, please sign below:
            </p>
            
            <div className="border-2 border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
              <canvas
                ref={signatureCanvasRef}
                width={500}
                height={200}
                className="w-full bg-white dark:bg-gray-100 touch-none"
                onMouseDown={handleStartDrawing}
                onMouseMove={handleDrawing}
                onMouseUp={handleEndDrawing}
                onMouseLeave={handleEndDrawing}
                onTouchStart={handleStartDrawing}
                onTouchMove={handleDrawing}
                onTouchEnd={handleEndDrawing}
              />
            </div>
            
            <div className="flex space-x-4">
              <button
                onClick={clearSignature}
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-dark-300 dark:hover:bg-dark-400 text-gray-800 dark:text-white rounded-lg transition-colors"
              >
                Clear
              </button>
              <button
                onClick={handleCancelSigning}
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-dark-300 dark:hover:bg-dark-400 text-gray-800 dark:text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCompleteSigning}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                Complete Signature
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {parties.map((party) => {
              const signature = signatures.find((sig) => sig.partyName === party);
              
              return (
                <div
                  key={party}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {party}
                    </h4>
                    {!signature && (
                      <button
                        onClick={() => handleStartSigning(party)}
                        className="px-4 py-1 bg-primary-600 hover:bg-primary-700 text-white rounded-lg text-sm transition-colors"
                      >
                        Sign
                      </button>
                    )}
                  </div>
                  
                  {signature ? (
                    <div>
                      <div className="h-20 mb-2">
                        <img
                          src={signature.signatureData || ''}
                          alt={`${party}'s signature`}
                          className="h-full object-contain"
                        />
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Signed on {signature.dateSigned?.toLocaleDateString()}
                      </p>
                    </div>
                  ) : (
                    <div className="h-20 flex items-center justify-center border border-dashed border-gray-300 dark:border-gray-600 rounded">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Awaiting signature
                      </p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  const simplifyText = (text: string) => {
    if (!isLegalMode) {
      // This is a placeholder for actual AI-powered simplification
      // In a real implementation, this would call an API to simplify legal text
      return text
        .replace(/hereinafter/g, 'from now on')
        .replace(/aforementioned/g, 'previously mentioned')
        .replace(/pursuant to/g, 'according to')
        .replace(/notwithstanding/g, 'despite')
        .replace(/in accordance with/g, 'following')
        .replace(/shall/g, 'will')
        .replace(/deemed to be/g, 'considered')
        .replace(/for the avoidance of doubt/g, 'to be clear')
        .replace(/in the event that/g, 'if')
        .replace(/prior to/g, 'before');
    }
    return text;
  };

  // Show guidance when contract type or form data is missing (after hooks)
  if (!contractType || !formData) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a contract type and complete the form to preview your contract.
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-dark-200 rounded-xl shadow-lg ${className}`}>
      {/* Preview Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Contract Preview
        </h2>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <span className="mr-2 text-sm text-gray-700 dark:text-gray-300">
              {isLegalMode ? 'Legal' : 'Simple'}
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={!isLegalMode}
                onChange={() => setIsLegalMode(!isLegalMode)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>
          
          <button
            onClick={() => window.print()}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            Export PDF
          </button>
        </div>
      </div>
      
      {/* Contract Document */}
      <div ref={previewScrollRef} className="p-6 md:p-8 max-h-[800px] overflow-y-auto">
        <div className="w-full max-w-none bg-white dark:bg-dark-100 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          {/* Contract Header */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {contractType.name} CONTRACT
            </h1>
            {formData.effectiveDate && (
              <p className="text-gray-600 dark:text-gray-400">
                Effective Date: {formatDate(formData.effectiveDate)}
              </p>
            )}
          </div>
          
          {/* Parties */}
          <div className="mb-6">
            <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
              PARTIES
            </h2>
            <p className="mb-3 text-gray-800 dark:text-gray-200">
              This {contractType.name} Contract (the "Agreement") is entered into by and between:
            </p>
            <p className="mb-2 text-gray-800 dark:text-gray-200">
              <strong>{formData.firstPartyName || '[First Party Name]'}</strong>, located at {formData.firstPartyAddress || '[First Party Address]'} (hereinafter referred to as the "First Party"), and
            </p>
            <p className="mb-2 text-gray-800 dark:text-gray-200">
              <strong>{formData.secondPartyName || '[Second Party Name]'}</strong>, located at {formData.secondPartyAddress || '[Second Party Address]'} (hereinafter referred to as the "Second Party").
            </p>
            <p className="text-gray-800 dark:text-gray-200">
              The First Party and Second Party may be referred to individually as a "Party" and collectively as the "Parties."
            </p>
          </div>
          
          {/* Clauses */}
          <div className="mb-6">
            {clauses.map((clause, index) => {
              const anchorId = `clause-${index + 1}-${slugify(clause.title)}`;
              return (
                <div key={clause.id} id={anchorId} className="mb-6 scroll-mt-24">
                  <div className="flex items-start justify-between gap-2">
                    <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                      {index + 1}. {clause.title}
                    </h2>
                    <div className="relative">
                      <a
                        href={`#${anchorId}`}
                        onClick={(e) => {
                          e.preventDefault();
                          if (typeof window !== 'undefined') {
                            const url = `${window.location.origin}${window.location.pathname}#${anchorId}`;
                            window.location.hash = anchorId;
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                              navigator.clipboard.writeText(url).then(() => {
                                setCopiedAnchor(anchorId);
                                if (copyTimeoutRef.current) window.clearTimeout(copyTimeoutRef.current);
                                copyTimeoutRef.current = window.setTimeout(() => setCopiedAnchor(null), 1200);
                              }).catch(() => {
                                // ignore clipboard errors
                              });
                            }
                          }
                        }}
                        className="mt-0.5 text-xs text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
                        title="Copy link to this clause"
                      >
                        #
                      </a>
                      {copiedAnchor === anchorId && (
                        <span className="absolute -top-6 right-0 text-[10px] px-2 py-0.5 rounded bg-gray-900 text-white dark:bg-white dark:text-gray-900 border border-gray-200 dark:border-gray-700">
                          Copied
                        </span>
                      )}
                    </div>
                  </div>
                  <p className="text-gray-800 dark:text-gray-200 whitespace-pre-line">
                    {simplifyText(clause.content)}
                  </p>
                </div>
              );
            })}
          </div>
          
          {/* General Terms */}
          {formData.governingLaw && (
            <div className="mb-6">
              <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                GOVERNING LAW
              </h2>
              <p className="text-gray-800 dark:text-gray-200">
                This Agreement shall be governed by and construed in accordance with the laws of {
                  (() => {
                    const lawMap: Record<string, string> = {
                      'us-ca': 'the State of California, United States',
                      'us-ny': 'the State of New York, United States',
                      'us-de': 'the State of Delaware, United States',
                      'uk': 'the United Kingdom',
                      'eu': 'the European Union',
                      'other': formData.notes || 'the jurisdiction specified in the notes',
                    };
                    return lawMap[formData.governingLaw as string] || formData.governingLaw as string;
                  })()
                }.
              </p>
            </div>
          )}
          
          {/* Additional Notes */}
          {formData.notes && (
            <div className="mb-6">
              <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                ADDITIONAL NOTES
              </h2>
              <p className="text-gray-800 dark:text-gray-200 whitespace-pre-line">
                {simplifyText(formData.notes)}
              </p>
            </div>
          )}
          
          {/* Signature Block */}
          <div className="mt-12">
            <p className="text-gray-800 dark:text-gray-200 mb-6">
              IN WITNESS WHEREOF, the Parties have executed this Agreement as of the Effective Date.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <p className="font-bold text-gray-900 dark:text-white mb-2">
                  {formData.firstPartyName || 'First Party'}:
                </p>
                <div className="h-20 mb-2">
                  {signatures.find(sig => sig.partyName === formData.firstPartyName) ? (
                    <img
                      src={signatures.find(sig => sig.partyName === formData.firstPartyName)?.signatureData || ''}
                      alt="First Party Signature"
                      className="h-full object-contain"
                    />
                  ) : (
                    <div className="h-full border border-dashed border-gray-300 dark:border-gray-600"></div>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Date: {signatures.find(sig => sig.partyName === formData.firstPartyName)?.dateSigned?.toLocaleDateString() || '_______________'}
                </p>
              </div>
              
              <div>
                <p className="font-bold text-gray-900 dark:text-white mb-2">
                  {formData.secondPartyName || 'Second Party'}:
                </p>
                <div className="h-20 mb-2">
                  {signatures.find(sig => sig.partyName === formData.secondPartyName) ? (
                    <img
                      src={signatures.find(sig => sig.partyName === formData.secondPartyName)?.signatureData || ''}
                      alt="Second Party Signature"
                      className="h-full object-contain"
                    />
                  ) : (
                    <div className="h-full border border-dashed border-gray-300 dark:border-gray-600"></div>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Date: {signatures.find(sig => sig.partyName === formData.secondPartyName)?.dateSigned?.toLocaleDateString() || '_______________'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Signature UI */}
      <div className="p-6 border-t border-gray-200 dark:border-gray-700">
        {renderSignatureSection()}
      </div>
    </div>
  );
};

export default ContractPreview;
