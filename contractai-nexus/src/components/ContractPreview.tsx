import React, { useState, useRef, useEffect } from 'react';
import { Clause } from '../types/clauses';
import { ContractType } from './ContractGalaxy';

interface ContractPreviewProps {
  contractType: ContractType | null;
  formData: Record<string, any>;
  clauses: Clause[];
  className?: string;
}

interface Signature {
  partyName: string;
  signatureData: string | null;
  dateSigned: Date | null;
}

const ContractPreview: React.FC<ContractPreviewProps> = ({
  contractType,
  formData,
  clauses,
  className = '',
}) => {
  const [signatures, setSignatures] = useState<Signature[]>([]);
  const [activeSigningParty, setActiveSigningParty] = useState<string | null>(null);
  const [isLegalMode, setIsLegalMode] = useState<boolean>(true);
  const signatureCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const previewScrollRef = useRef<HTMLDivElement | null>(null);
  const lastScrollTopRef = useRef<number>(0);
  const [lastPosition, setLastPosition] = useState<{ x: number; y: number } | null>(null);
  const [copiedAnchor, setCopiedAnchor] = useState<string | null>(null);
  const copyTimeoutRef = useRef<number | null>(null);

  // Early return moved below hooks to satisfy React hook rules

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const slugify = (text: string) =>
    (text || '')
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .trim()
      .replace(/\s+/g, '-');

  // Preserve scroll position across clause reorders
  useEffect(() => {
    const el = previewScrollRef.current;
    if (!el) return;
    const onScroll = () => {
      lastScrollTopRef.current = el.scrollTop;
    };
    el.addEventListener('scroll', onScroll, { passive: true });
    return () => {
      el.removeEventListener('scroll', onScroll);
    };
  }, []);

  useEffect(() => {
    const el = previewScrollRef.current;
    if (!el) return;
    // Restore last scrollTop after clauses changed
    el.scrollTop = lastScrollTopRef.current;
  }, [clauses]);

  useEffect(() => {
    return () => {
      if (copyTimeoutRef.current) {
        window.clearTimeout(copyTimeoutRef.current);
      }
    };
  }, []);

  const getPartyNames = () => {
    const parties = [];
    if (formData.firstPartyName) {
      parties.push(formData.firstPartyName);
    }
    if (formData.secondPartyName) {
      parties.push(formData.secondPartyName);
    }
    return parties;
  };

  const handleStartSigning = (partyName: string) => {
    setActiveSigningParty(partyName);
    
    // Clear the canvas
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const handleCancelSigning = () => {
    setActiveSigningParty(null);
  };

  const handleCompleteSigning = () => {
    if (!activeSigningParty) return;
    
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const signatureData = canvas.toDataURL('image/png');
      
      setSignatures((prev) => [
        ...prev.filter((sig) => sig.partyName !== activeSigningParty),
        {
          partyName: activeSigningParty,
          signatureData,
          dateSigned: new Date(),
        },
      ]);
      
      setActiveSigningParty(null);
    }
  };

  const handleStartDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    let clientX, clientY;
    
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;
    
    setLastPosition({ x, y });
  };

  const handleDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !lastPosition) return;
    
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const rect = canvas.getBoundingClientRect();
    let clientX, clientY;
    
    if ('touches' in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
      e.preventDefault(); // Prevent scrolling on touch devices
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;
    
    ctx.beginPath();
    ctx.moveTo(lastPosition.x, lastPosition.y);
    ctx.lineTo(x, y);
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.stroke();
    ctx.closePath();
    
    setLastPosition({ x, y });
  };

  const handleEndDrawing = () => {
    setIsDrawing(false);
    setLastPosition(null);
  };

  const clearSignature = () => {
    const canvas = signatureCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const renderSignatureSection = () => {
    const parties = getPartyNames();

    return (
      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Digital Signatures
          </h3>
        </div>

        {activeSigningParty ? (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl p-6">
            <div className="text-center mb-6">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-full mb-4">
                <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Signing as: {activeSigningParty}
                </span>
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                Please draw your signature in the area below
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl overflow-hidden mb-6">
              <canvas
                ref={signatureCanvasRef}
                width={500}
                height={200}
                className="w-full bg-white dark:bg-gray-100 touch-none cursor-crosshair"
                onMouseDown={handleStartDrawing}
                onMouseMove={handleDrawing}
                onMouseUp={handleEndDrawing}
                onMouseLeave={handleEndDrawing}
                onTouchStart={handleStartDrawing}
                onTouchMove={handleDrawing}
                onTouchEnd={handleEndDrawing}
              />
            </div>

            <div className="flex flex-wrap gap-3 justify-center">
              <button
                onClick={clearSignature}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Clear
              </button>
              <button
                onClick={handleCancelSigning}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Cancel
              </button>
              <button
                onClick={handleCompleteSigning}
                className="inline-flex items-center gap-2 px-6 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-xl transition-colors font-medium"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Complete Signature
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {parties.map((party, index) => {
              const signature = signatures.find((sig) => sig.partyName === party);
              const isFirstParty = index === 0;

              return (
                <div
                  key={party}
                  className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:border-primary-300 dark:hover:border-primary-600 transition-colors"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        isFirstParty
                          ? 'bg-primary-100 dark:bg-primary-900/30'
                          : 'bg-secondary-100 dark:bg-secondary-900/30'
                      }`}>
                        <span className={`font-bold text-sm ${
                          isFirstParty
                            ? 'text-primary-600 dark:text-primary-400'
                            : 'text-secondary-600 dark:text-secondary-400'
                        }`}>
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {party}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {isFirstParty ? 'Primary Party' : 'Secondary Party'}
                        </p>
                      </div>
                    </div>

                    {signature ? (
                      <div className="flex items-center gap-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full">
                        <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-xs font-medium text-green-800 dark:text-green-200">
                          Signed
                        </span>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleStartSigning(party)}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white text-sm font-medium rounded-xl transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                        Sign Contract
                      </button>
                    )}
                  </div>

                  {signature ? (
                    <div className="space-y-3">
                      <div className="h-20 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg flex items-center justify-center p-2">
                        <img
                          src={signature.signatureData || ''}
                          alt={`${party}'s signature`}
                          className="h-full object-contain"
                        />
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span>
                          Signed on {signature.dateSigned?.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="h-20 flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700/50">
                      <div className="text-center">
                        <svg className="w-6 h-6 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Awaiting signature
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  const simplifyText = (text: string) => {
    if (!isLegalMode) {
      // This is a placeholder for actual AI-powered simplification
      // In a real implementation, this would call an API to simplify legal text
      return text
        .replace(/hereinafter/g, 'from now on')
        .replace(/aforementioned/g, 'previously mentioned')
        .replace(/pursuant to/g, 'according to')
        .replace(/notwithstanding/g, 'despite')
        .replace(/in accordance with/g, 'following')
        .replace(/shall/g, 'will')
        .replace(/deemed to be/g, 'considered')
        .replace(/for the avoidance of doubt/g, 'to be clear')
        .replace(/in the event that/g, 'if')
        .replace(/prior to/g, 'before');
    }
    return text;
  };

  // Show guidance when contract type or form data is missing (after hooks)
  if (!contractType || !formData) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a contract type and complete the form to preview your contract.
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-900 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Enhanced Preview Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-t-2xl">
        <div className="mb-4 sm:mb-0">
          <h2 className="text-2xl font-display font-bold text-gray-900 dark:text-white mb-1">
            Contract Preview
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {contractType?.name} • {clauses.length} clause{clauses.length !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          {/* Reading Mode Toggle */}
          <div className="flex items-center gap-2 bg-white dark:bg-gray-800 rounded-xl p-1 border border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setIsLegalMode(true)}
              className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all ${
                isLegalMode
                  ? 'bg-primary-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Legal
            </button>
            <button
              onClick={() => setIsLegalMode(false)}
              className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all ${
                !isLegalMode
                  ? 'bg-primary-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              Simple
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => window.print()}
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white text-sm font-medium rounded-xl transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export PDF
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Contract Document */}
      <div ref={previewScrollRef} className="p-6 max-h-[800px] overflow-y-auto">
        <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Document Paper Effect */}
          <div className="bg-gradient-to-b from-gray-50 to-white dark:from-gray-700 dark:to-gray-800 p-8 md:p-12">
            {/* Enhanced Contract Header */}
            <div className="text-center mb-12 pb-8 border-b-2 border-gray-200 dark:border-gray-600">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-2xl mb-4">
                <svg className="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h1 className="text-3xl md:text-4xl font-display font-bold text-gray-900 dark:text-white mb-3 tracking-tight">
                {contractType.name.toUpperCase()} CONTRACT
              </h1>
              {formData.effectiveDate && (
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-full">
                  <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Effective Date: {formatDate(formData.effectiveDate)}
                  </span>
                </div>
              )}
            </div>

            {/* Enhanced Parties Section */}
            <div className="mb-10">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  CONTRACTING PARTIES
                </h2>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6 mb-6">
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  This {contractType.name} Contract (the "Agreement") is entered into by and between the following parties:
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* First Party */}
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 dark:text-primary-400 font-bold text-sm">1</span>
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 dark:text-white">First Party</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Primary Contracting Entity</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-gray-800 dark:text-gray-200">
                      <span className="font-semibold">{formData.firstPartyName || '[First Party Name]'}</span>
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formData.firstPartyAddress || '[First Party Address]'}
                    </p>
                  </div>
                </div>

                {/* Second Party */}
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-secondary-100 dark:bg-secondary-900/30 rounded-full flex items-center justify-center">
                      <span className="text-secondary-600 dark:text-secondary-400 font-bold text-sm">2</span>
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 dark:text-white">Second Party</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Secondary Contracting Entity</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-gray-800 dark:text-gray-200">
                      <span className="font-semibold">{formData.secondPartyName || '[Second Party Name]'}</span>
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formData.secondPartyAddress || '[Second Party Address]'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Note:</strong> The parties above may be referred to individually as a "Party" and collectively as the "Parties" throughout this agreement.
                </p>
              </div>
            </div>

            {/* Enhanced Clauses Section */}
            <div className="mb-10">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  CONTRACT TERMS & CONDITIONS
                </h2>
              </div>

              <div className="space-y-8">
                {clauses.map((clause, index) => {
                  const anchorId = `clause-${index + 1}-${slugify(clause.title)}`;
                  return (
                    <div key={clause.id} id={anchorId} className="scroll-mt-24">
                      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-6 hover:border-primary-300 dark:hover:border-primary-600 transition-colors">
                        <div className="flex items-start justify-between gap-4 mb-4">
                          <div className="flex items-start gap-4">
                            <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-sm font-bold text-gray-600 dark:text-gray-400">
                                {index + 1}
                              </span>
                            </div>
                            <div>
                              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                                {clause.title}
                              </h3>
                              <div className="flex items-center gap-2 mb-3">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  clause.complexity === 'simple' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                  clause.complexity === 'standard' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                                  'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                                }`}>
                                  {clause.complexity}
                                </span>
                                {clause.aiGenerated && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                                    AI Generated
                                  </span>
                                )}
                                {clause.isRequired && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                    Required
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="relative">
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                if (typeof window !== 'undefined') {
                                  const url = `${window.location.origin}${window.location.pathname}#${anchorId}`;
                                  window.location.hash = anchorId;
                                  if (navigator.clipboard && navigator.clipboard.writeText) {
                                    navigator.clipboard.writeText(url).then(() => {
                                      setCopiedAnchor(anchorId);
                                      if (copyTimeoutRef.current) window.clearTimeout(copyTimeoutRef.current);
                                      copyTimeoutRef.current = window.setTimeout(() => setCopiedAnchor(null), 1200);
                                    }).catch(() => {
                                      // ignore clipboard errors
                                    });
                                  }
                                }
                              }}
                              className="p-2 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                              title="Copy link to this clause"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                              </svg>
                            </button>
                            {copiedAnchor === anchorId && (
                              <div className="absolute -top-10 right-0 bg-gray-900 dark:bg-white text-white dark:text-gray-900 text-xs px-2 py-1 rounded-lg shadow-lg">
                                Link copied!
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="prose prose-sm max-w-none">
                          <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
                            {isLegalMode ? clause.content : simplifyText(clause.content)}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Enhanced General Terms */}
            {formData.governingLaw && (
              <div className="mb-10">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    GOVERNING LAW
                  </h2>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-xl p-6">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    This Agreement shall be governed by and construed in accordance with the laws of{' '}
                    <span className="font-semibold text-purple-800 dark:text-purple-200">
                      {(() => {
                        const lawMap: Record<string, string> = {
                          'us-ca': 'the State of California, United States',
                          'us-ny': 'the State of New York, United States',
                          'us-de': 'the State of Delaware, United States',
                          'uk': 'the United Kingdom',
                          'eu': 'the European Union',
                          'other': formData.notes || 'the jurisdiction specified in the notes',
                        };
                        return lawMap[formData.governingLaw as string] || formData.governingLaw as string;
                      })()}
                    </span>.
                  </p>
                </div>
              </div>
            )}

            {/* Enhanced Additional Notes */}
            {formData.notes && (
              <div className="mb-10">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    ADDITIONAL PROVISIONS
                  </h2>
                </div>

                <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-6">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
                    {isLegalMode ? formData.notes : simplifyText(formData.notes)}
                  </p>
                </div>
              </div>
            )}

            {/* Enhanced Signature Block */}
            <div className="mt-16 pt-8 border-t-2 border-gray-200 dark:border-gray-600">
              <div className="text-center mb-8">
                <div className="inline-flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    EXECUTION
                  </h2>
                </div>
                <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                  IN WITNESS WHEREOF, the Parties have executed this Agreement as of the Effective Date first written above.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* First Party Signature */}
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 dark:text-primary-400 font-bold text-sm">1</span>
                    </div>
                    <h3 className="font-bold text-gray-900 dark:text-white">
                      {formData.firstPartyName || 'First Party'}
                    </h3>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Signature:
                    </label>
                    <div className="h-24 bg-gray-50 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center">
                      {signatures.find(sig => sig.partyName === formData.firstPartyName) ? (
                        <img
                          src={signatures.find(sig => sig.partyName === formData.firstPartyName)?.signatureData || ''}
                          alt="First Party Signature"
                          className="h-full object-contain"
                        />
                      ) : (
                        <span className="text-gray-400 dark:text-gray-500 text-sm">Signature required</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>
                      Date: {signatures.find(sig => sig.partyName === formData.firstPartyName)?.dateSigned?.toLocaleDateString() || '_______________'}
                    </span>
                  </div>
                </div>

                {/* Second Party Signature */}
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-secondary-100 dark:bg-secondary-900/30 rounded-full flex items-center justify-center">
                      <span className="text-secondary-600 dark:text-secondary-400 font-bold text-sm">2</span>
                    </div>
                    <h3 className="font-bold text-gray-900 dark:text-white">
                      {formData.secondPartyName || 'Second Party'}
                    </h3>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Signature:
                    </label>
                    <div className="h-24 bg-gray-50 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center">
                      {signatures.find(sig => sig.partyName === formData.secondPartyName) ? (
                        <img
                          src={signatures.find(sig => sig.partyName === formData.secondPartyName)?.signatureData || ''}
                          alt="Second Party Signature"
                          className="h-full object-contain"
                        />
                      ) : (
                        <span className="text-gray-400 dark:text-gray-500 text-sm">Signature required</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>
                      Date: {signatures.find(sig => sig.partyName === formData.secondPartyName)?.dateSigned?.toLocaleDateString() || '_______________'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Signature UI */}
      <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-b-2xl">
        {renderSignatureSection()}
      </div>
    </div>
  );
};

export default ContractPreview;
