import React from 'react';
import { motion } from 'framer-motion';

export enum CreationStage {
  SELECT_TYPE,
  FILL_FORM,
  BUILD_CLAUSES,
  PREVIEW,
}

interface StageInfo {
  name: string;
  description: string;
  icon: React.ReactNode;
}

interface ProgressTrackerProps {
  currentStage: CreationStage;
  onStageClick?: (stage: CreationStage) => void;
  allowNavigation?: boolean;
  className?: string;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  currentStage,
  onStageClick,
  allowNavigation = false,
  className = '',
}) => {
  // Define stage information with icons and descriptions
  const stages: Record<CreationStage, StageInfo> = {
    [CreationStage.SELECT_TYPE]: {
      name: 'Select Type',
      description: 'Choose the type of contract',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
    },
    [CreationStage.FILL_FORM]: {
      name: 'Fill Details',
      description: 'Enter contract information',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
        </svg>
      ),
    },
    [CreationStage.BUILD_CLAUSES]: {
      name: 'Build Clauses',
      description: 'Add and customize clauses',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
        </svg>
      ),
    },
    [CreationStage.PREVIEW]: {
      name: 'Preview & Sign',
      description: 'Review and finalize contract',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
          <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
    },
  };

  // Get all stage values as numbers
  const stageValues = Object.values(CreationStage).filter(
    (v) => !isNaN(Number(v))
  ) as number[];

  // Handle stage click
  const handleStageClick = (stage: CreationStage) => {
    if (allowNavigation && onStageClick && stage <= currentStage) {
      onStageClick(stage);
    }
  };

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      {/* Simple Progress Bar Container */}
      <div className="relative mb-8">
        {/* Background Track */}
        <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gray-200 dark:bg-gray-700 -translate-y-1/2" />
        
        {/* Simple Progress Fill */}
        <motion.div 
          className="absolute top-1/2 left-0 h-0.5 bg-blue-600 dark:bg-blue-500 -translate-y-1/2"
          initial={{ width: '0%' }}
          animate={{ 
            width: `${(currentStage / (stageValues.length - 1)) * 100}%` 
          }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        />
        
        {/* Simple Stage Markers */}
        <div className="relative flex justify-between items-center">
          {stageValues.map((stageValue) => {
            const stage = stageValue as CreationStage;
            const stageInfo = stages[stage];
            const isActive = stage <= currentStage;
            const isCurrent = stage === currentStage;
            
            return (
              <div 
                key={stage} 
                className="flex flex-col items-center"
                onClick={() => handleStageClick(stage)}
              >
                {/* Simple Stage Circle */}
                <div
                  className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full cursor-pointer
                    ${isActive 
                      ? 'bg-blue-600 dark:bg-blue-500 text-white' 
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }
                    ${allowNavigation && stage <= currentStage ? 'hover:opacity-80' : ''}
                    transition-colors duration-200
                  `}
                >
                  {/* Simple Icon */}
                  <div className="w-4 h-4">
                    {stageInfo.icon}
                  </div>
                  
                  {/* Simple Checkmark for completed stages */}
                  {isActive && stage < currentStage && (
                    <div className="absolute -top-1 -right-1 bg-green-500 rounded-full p-0.5 border-2 border-white dark:border-gray-900">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-2 w-2 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
                
                {/* Simple Stage Name */}
                <div className="mt-2 text-center">
                  <div 
                    className={`text-xs font-medium
                      ${isActive 
                        ? 'text-blue-600 dark:text-blue-400' 
                        : 'text-gray-500 dark:text-gray-400'
                      }
                    `}
                  >
                    {stageInfo.name}
                  </div>
                  
                  {/* Simple Stage Description */}
                  {isCurrent && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {stageInfo.description}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
