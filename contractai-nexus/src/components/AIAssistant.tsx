import React, { useState, useEffect, useRef } from 'react';
import { ContractType } from './ContractGalaxy';
import { Clause } from '../types/clauses';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
}

interface AIAssistantProps {
  contractType: ContractType | null;
  formData: Record<string, any>;
  clauses: Clause[];
  className?: string;
  isOpen: boolean;
  onClose: () => void;
}

// Helper function to generate contextual suggestions based on contract state
const generateSuggestions = (
  contractType: ContractType | null,
  formData: Record<string, any>,
  clauses: Clause[]
): string[] => {
  const suggestions: string[] = [
    "How can I help you with your contract today?",
    "Need help understanding a specific clause?",
    "Would you like me to suggest additional clauses for your contract?",
  ];

  // Add contract type specific suggestions
  if (contractType) {
    switch (contractType.id) {
      case 'nda':
        suggestions.push("Would you like me to explain confidentiality terms?");
        suggestions.push("Do you need help with the scope of protected information?");
        break;
      case 'employment':
        suggestions.push("Would you like me to review the compensation terms?");
        suggestions.push("Do you need help with employment benefits or termination clauses?");
        break;
      case 'service':
        suggestions.push("Would you like me to help define the scope of services?");
        suggestions.push("Do you need help with payment terms or deliverables?");
        break;
    }
  }

  // Add form data specific suggestions
  if (formData) {
    if (!formData.effectiveDate) {
      suggestions.push("Don't forget to set an effective date for your contract.");
    }
    if (!formData.governingLaw) {
      suggestions.push("I recommend specifying the governing law for your contract.");
    }
  }

  // Add clause specific suggestions
  if (clauses.length === 0) {
    suggestions.push("Your contract doesn't have any clauses yet. Would you like me to suggest some?");
  } else if (clauses.length < 3) {
    suggestions.push("Your contract might benefit from additional clauses. Would you like suggestions?");
  }

  // Shuffle and return a subset of suggestions
  return shuffleArray(suggestions).slice(0, 3);
};

// Helper function to shuffle an array
const shuffleArray = <T,>(array: T[]): T[] => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

// Mock AI responses based on user input
const getAIResponse = (
  message: string,
  contractType: ContractType | null,
  formData: Record<string, any>,
  clauses: Clause[]
): Promise<string> => {
  // This would be replaced with actual API calls to an AI service
  return new Promise((resolve) => {
    setTimeout(() => {
      // Simple keyword-based responses
      if (message.toLowerCase().includes('confidentiality') || message.toLowerCase().includes('nda')) {
        resolve("Confidentiality clauses protect sensitive information shared between parties. They typically define what constitutes confidential information, how it can be used, and for how long the obligation lasts. Would you like me to suggest a specific confidentiality clause for your contract?");
      } else if (message.toLowerCase().includes('termination')) {
        resolve("Termination clauses specify how and when a contract can be ended. They typically include notice periods, conditions for termination, and any obligations that survive termination. Would you like me to help you draft a termination clause?");
      } else if (message.toLowerCase().includes('payment') || message.toLowerCase().includes('compensation')) {
        resolve("Payment terms should clearly specify amounts, payment schedules, late payment penalties, and acceptable payment methods. Would you like me to help you draft comprehensive payment terms for your contract?");
      } else if (message.toLowerCase().includes('suggest') || message.toLowerCase().includes('recommendation')) {
        if (contractType) {
          switch (contractType.id) {
            case 'nda':
              resolve("For an NDA, I recommend including clauses on: 1) Definition of confidential information, 2) Exclusions from confidential information, 3) Obligations of the receiving party, 4) Term of confidentiality, and 5) Remedies for breach. Would you like me to elaborate on any of these?");
              break;
            case 'employment':
              resolve("For an employment contract, I recommend including clauses on: 1) Job responsibilities, 2) Compensation and benefits, 3) Work schedule, 4) Term of employment, 5) Termination conditions, and 6) Non-compete provisions. Would you like me to elaborate on any of these?");
              break;
            case 'service':
              resolve("For a service agreement, I recommend including clauses on: 1) Scope of services, 2) Deliverables and timelines, 3) Payment terms, 4) Intellectual property rights, 5) Warranties, and 6) Limitation of liability. Would you like me to elaborate on any of these?");
              break;
            default:
              resolve("Based on your contract type, I recommend including clauses on terms, termination, liability, and dispute resolution. Would you like me to suggest specific clauses for your contract?");
          }
        } else {
          resolve("I'd be happy to suggest clauses for your contract. Could you tell me more about what type of contract you're creating?");
        }
      } else if (message.toLowerCase().includes('help') || message.toLowerCase().includes('explain')) {
        resolve("I'm here to help! I can explain legal terms, suggest clauses, help you understand contract requirements, or answer questions about contract law. What specific aspect of your contract would you like help with?");
      } else {
        resolve("I understand you're working on a contract. I can help with drafting clauses, explaining legal terms, or providing suggestions to improve your contract. Could you tell me more about what you need assistance with?");
      }
    }, 1000);
  });
};

const AIAssistant: React.FC<AIAssistantProps> = ({
  contractType,
  formData,
  clauses,
  className = '',
  isOpen,
  onClose,
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm your AI contract assistant. How can I help you today?",
      sender: 'assistant',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Generate contextual suggestions when contract data changes
  useEffect(() => {
    if (isOpen) {
      setSuggestions(generateSuggestions(contractType, formData, clauses));
    }
  }, [isOpen, contractType, formData, clauses]);

  // Scroll to bottom of messages when new ones are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      const response = await getAIResponse(inputValue, contractType, formData, clauses);
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "I'm sorry, I encountered an error processing your request. Please try again.",
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      // Generate new suggestions after conversation
      setSuggestions(generateSuggestions(contractType, formData, clauses));
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-y-0 right-0 w-80 md:w-96 bg-white dark:bg-dark-200 shadow-lg flex flex-col z-50 transition-transform transform ${isOpen ? 'translate-x-0' : 'translate-x-full'} ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          AI Assistant
        </h2>
        <button
          onClick={onClose}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-dark-300 text-gray-500 dark:text-gray-400"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message: Message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.sender === 'user'
                  ? 'bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100'
                  : 'bg-gray-100 dark:bg-dark-300 text-gray-800 dark:text-gray-200'
              }`}
            >
              <p className="whitespace-pre-wrap">{message.text}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-dark-300 rounded-lg p-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      {/* Suggestions */}
      {suggestions.length > 0 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Suggestions:
          </p>
          <div className="flex flex-wrap gap-2">
            {suggestions.map((suggestion: string, index: number) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="text-xs bg-gray-100 hover:bg-gray-200 dark:bg-dark-300 dark:hover:bg-dark-400 text-gray-800 dark:text-gray-200 rounded-full px-3 py-1 transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}
      
      {/* Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-end space-x-2">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1 min-h-[40px] max-h-32 p-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-dark-300 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
            rows={1}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            className={`p-3 rounded-lg ${
              !inputValue.trim() || isTyping
                ? 'bg-gray-300 dark:bg-dark-400 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                : 'bg-primary-600 hover:bg-primary-700 text-white'
            }`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
