import React, { useState } from 'react';
import AdaptiveNeuralNetwork, { ContractType, contractTypes } from './AdaptiveNeuralNetwork';
import { motion } from 'framer-motion';

// Re-export ContractType for backward compatibility
export type { ContractType } from './AdaptiveNeuralNetwork';
export { contractTypes } from './AdaptiveNeuralNetwork';

// Quick Select Component
const QuickSelect: React.FC<{
  onSelectContract: (contract: ContractType | null) => void;
}> = ({ onSelectContract }) => {
  const handleQuickSelect = (contract: ContractType) => {
    onSelectContract(contract);
  };

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Quick Select Contract Type
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Already know what you need? Choose directly from our contract types.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {contractTypes.map((contract, index) => (
          <motion.div
            key={contract.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="bg-white dark:bg-dark-200 rounded-lg border border-gray-200 dark:border-dark-300 p-4 cursor-pointer hover:shadow-lg transition-all duration-200"
            onClick={() => handleQuickSelect(contract)}
          >
            <div className="flex items-center mb-3">
              <div 
                className="w-4 h-4 rounded-full mr-3"
                style={{ backgroundColor: contract.color }}
              />
              <h4 className="font-semibold text-gray-900 dark:text-white">
                {contract.name}
              </h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {contract.description}
            </p>
            <div className="mt-3 flex justify-end">
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                Select →
              </span>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Main Contract Galaxy Component (Enhanced)
const ContractGalaxy: React.FC<{
  onSelectContract: (contract: ContractType | null) => void;
  className?: string;
}> = ({ onSelectContract, className = '' }) => {
  const [viewMode, setViewMode] = useState<'guided' | 'quick'>('guided');

  return (
    <div className={`w-full min-h-[400px] ${className}`}>
      {/* View Toggle */}
      <div className="flex justify-center mb-6">
        <div className="flex bg-gray-100 dark:bg-dark-300 rounded-lg p-1">
          <button
            onClick={() => setViewMode('guided')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              viewMode === 'guided'
                ? 'bg-white dark:bg-dark-200 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            🧠 Guided Selection
          </button>
          <button
            onClick={() => setViewMode('quick')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              viewMode === 'quick'
                ? 'bg-white dark:bg-dark-200 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            ⚡ Quick Select
          </button>
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === 'guided' ? (
        <AdaptiveNeuralNetwork onSelectContract={onSelectContract} />
      ) : (
        <QuickSelect onSelectContract={onSelectContract} />
      )}
    </div>
  );
};

export default ContractGalaxy;
