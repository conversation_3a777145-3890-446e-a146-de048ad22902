import React from 'react';
import { motion } from 'framer-motion';
import { useExpressMode } from '../contexts/ExpressModeContext';
import { Zap, Settings } from 'lucide-react';

const ExpressModeToggle: React.FC = () => {
  const { isExpressMode, toggleExpressMode } = useExpressMode();

  return (
    <div className="flex items-center space-x-3 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center space-x-2">
        <Zap className={`w-4 h-4 ${isExpressMode ? 'text-blue-600' : 'text-gray-400'}`} />
        <span className={`text-sm font-medium ${isExpressMode ? 'text-blue-600' : 'text-gray-500'}`}>
          Quick
        </span>
      </div>
      
      <motion.button
        onClick={toggleExpressMode}
        className={`relative w-12 h-6 rounded-full transition-colors duration-200 ${
          isExpressMode ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
        }`}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          className="absolute top-0.5 w-5 h-5 bg-white rounded-full shadow-sm"
          animate={{
            x: isExpressMode ? 1 : 25,
          }}
          transition={{
            type: "spring",
            stiffness: 500,
            damping: 30,
          }}
        />
      </motion.button>
      
      <div className="flex items-center space-x-2">
        <span className={`text-sm font-medium ${!isExpressMode ? 'text-blue-600' : 'text-gray-500'}`}>
          Detailed
        </span>
        <Settings className={`w-4 h-4 ${!isExpressMode ? 'text-blue-600' : 'text-gray-400'}`} />
      </div>
    </div>
  );
};

export default ExpressModeToggle;
