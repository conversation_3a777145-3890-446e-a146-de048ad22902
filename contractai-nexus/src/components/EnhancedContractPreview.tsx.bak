// This file is now a re-export of the modular implementation
export { default } from './EnhancedContractPreview/index';
export * from './EnhancedContractPreview/index';
  // Main render
  return (
    <div className={`bg-white dark:bg-dark-200 rounded-xl shadow-lg ${className}`}>
      {/* Preview Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Contract Preview
        </h2>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <span className="mr-2 text-sm text-gray-700 dark:text-gray-300">
              {isLegalMode ? 'Legal' : 'Simple'}
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={!isLegalMode}
                onChange={() => setIsLegalMode(!isLegalMode)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>
      
      {/* Tab Navigation */}
      {renderTabNavigation()}
      
      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'preview' && (
          <div>
            {/* Export Options */}
            <div className="flex justify-end mb-4 space-x-3">
              <button
                onClick={() => {
                  alert('PDF export simulation - In a production environment, this would generate a PDF document');
                  // Simulate download delay
                  setTimeout(() => {
                    alert('PDF exported successfully!');
                  }, 1500);
                }}
                className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export PDF
              </button>
              <button
                onClick={() => {
                  alert('Word export simulation - In a production environment, this would generate a DOCX document');
                  // Simulate download delay
                  setTimeout(() => {
                    alert('Word document exported successfully!');
                  }, 1500);
                }}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export Word
              </button>
              <button
                onClick={() => {
                  // Save current contract as a version
                  const newVersion = {
                    id: `v-${Date.now()}`,
                    name: `Version ${versions.length + 1}`,
                    timestamp: new Date(),
                    content: JSON.stringify(formData),
                    clauses: [...clauses]
                  };
                  setVersions([...versions, newVersion]);
                  alert('Contract version saved successfully!');
                }}
                className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                Save Version
              </button>
            </div>
            
            {/* Contract Document */}
            <div className="max-h-[800px] overflow-y-auto">
              <div className="max-w-4xl mx-auto bg-white dark:bg-dark-100 p-8 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                {/* Contract Header */}
                <div className="text-center mb-8">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {contractType?.name} CONTRACT
                  </h1>
                  {formData.effectiveDate && (
                    <p className="text-gray-600 dark:text-gray-400">
                      Effective Date: {formatDate(formData.effectiveDate)}
                    </p>
                  )}
                </div>
                
                {/* Parties */}
                <div className="mb-6">
                  <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                    PARTIES
                  </h2>
                  <p className="mb-3 text-gray-800 dark:text-gray-200">
                    This {contractType?.name} Contract (the "Agreement") is entered into by and between:
                  </p>
                  <p className="mb-2 text-gray-800 dark:text-gray-200">
                    <strong>{formData.firstPartyName || '[First Party Name]'}</strong>, located at {formData.firstPartyAddress || '[First Party Address]'} (hereinafter referred to as the "First Party"), and
                  </p>
                  <p className="mb-2 text-gray-800 dark:text-gray-200">
                    <strong>{formData.secondPartyName || '[Second Party Name]'}</strong>, located at {formData.secondPartyAddress || '[Second Party Address]'} (hereinafter referred to as the "Second Party").
                  </p>
                  <p className="text-gray-800 dark:text-gray-200">
                    The First Party and Second Party may be referred to individually as a "Party" and collectively as the "Parties."
                  </p>
                </div>
                
                {/* Clauses with Rich Text Formatting */}
                <div className="mb-6">
                  {clauses.map((clause, index) => {
                    // Apply rich text formatting
                    const formattedContent = clause.content
                      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
                      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
                      .replace(/__(.*?)__/g, '<u>$1</u>') // Underline
                      .replace(/```(.*?)```/g, '<pre class="bg-gray-100 dark:bg-gray-800 p-2 rounded my-2 overflow-x-auto">$1</pre>') // Code block
                      .replace(/\n/g, '<br />'); // Line breaks

                    return (
                      <div key={clause.id} className="mb-6">
                        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                          {index + 1}. {clause.title}
                        </h2>
                        <div 
                          className="text-gray-800 dark:text-gray-200 rich-text-content"
                          dangerouslySetInnerHTML={{ __html: isLegalMode ? formattedContent : simplifyText(formattedContent) }}
                        />
                      </div>
                    );
                  })}
                </div>
                
                {/* General Terms */}
                {formData.governingLaw && (
                  <div className="mb-6">
                    <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                      GOVERNING LAW
                    </h2>
                    <p className="text-gray-800 dark:text-gray-200">
                      This Agreement shall be governed by and construed in accordance with the laws of {
                        (() => {
                          const lawMap: Record<string, string> = {
                            'us-ca': 'the State of California, United States',
                            'us-ny': 'the State of New York, United States',
                            'us-de': 'the State of Delaware, United States',
                            'uk': 'the United Kingdom',
                            'eu': 'the European Union',
                            'other': formData.notes || 'the jurisdiction specified in the notes',
                          };
                          return lawMap[formData.governingLaw as string] || formData.governingLaw as string;
                        })()
                      }.
                    </p>
                  </div>
                )}
                
                {/* Signature Placeholders */}
                <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                  <div className="grid grid-cols-2 gap-8">
                    <div>
                      <p className="mb-1 text-gray-600 dark:text-gray-400">First Party:</p>
                      <p className="font-bold">{formData.firstPartyName || '[First Party Name]'}</p>
                      <div className="h-16 mt-4 border-b border-gray-400 dark:border-gray-600"></div>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">Signature</p>
                    </div>
                    <div>
                      <p className="mb-1 text-gray-600 dark:text-gray-400">Second Party:</p>
                      <p className="font-bold">{formData.secondPartyName || '[Second Party Name]'}</p>
                      <div className="h-16 mt-4 border-b border-gray-400 dark:border-gray-600"></div>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">Signature</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )</div>
      )}
        
        {activeTab === 'versions' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-800 dark:text-white">Contract Version History</h2>
                    }}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Restore Selected Version
                  </button>
                )}
                <button
                  onClick={() => {
                    // Create a comparison view between selected version and current
                    if (!activeVersion) {
                      alert('Please select a version to compare');
                      return;
                    }
                    
                    setIsComparing(!isComparing);
                  }}
                  className={`px-4 py-2 ${isComparing ? 'bg-gray-600 hover:bg-gray-700' : 'bg-purple-600 hover:bg-purple-700'} text-white rounded-lg transition-colors flex items-center`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                  {isComparing ? 'Exit Comparison' : 'Compare With Current'}
                </button>
              </div>
            </div>
            
            {isComparing && activeVersion ? (
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h3 className="text-lg font-bold mb-2 text-blue-600 dark:text-blue-400">Selected Version: {activeVersion.name}</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Created on {activeVersion.timestamp.toLocaleString()}
                  </p>
                  <div className="max-h-[500px] overflow-y-auto">
                    {/* Display selected version clauses */}
                    {activeVersion.clauses.map((clause, index) => (
                      <div key={`old-${clause.id}`} className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded">
                        <h4 className="font-bold">{index + 1}. {clause.title}</h4>
                        <p className="whitespace-pre-wrap">{clause.content}</p>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <h3 className="text-lg font-bold mb-2 text-green-600 dark:text-green-400">Current Version</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Working draft
                  </p>
                  <div className="max-h-[500px] overflow-y-auto">
                    {/* Display current clauses */}
                    {clauses.map((clause, index) => (
                      <div key={`current-${clause.id}`} className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded">
                        <h4 className="font-bold">{index + 1}. {clause.title}</h4>
                        <p className="whitespace-pre-wrap">{clause.content}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                {versions.length === 0 ? (
                  <div className="p-8 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Versions Saved Yet</h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                      Save versions of your contract to track changes over time.
                    </p>
                    <button
                      onClick={() => {
                        // Save current contract as a version
                        const newVersion = {
                          id: `v-${Date.now()}`,
                          name: `Version ${versions.length + 1}`,
                          timestamp: new Date(),
                          content: JSON.stringify(formData),
                          clauses: [...clauses]
                        };
                        setVersions([...versions, newVersion]);
                        alert('Contract version saved successfully!');
                      }}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                    >
                      Save Current Version
                    </button>
                  </div>
                ) : (
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Version
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Created
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Clauses
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {versions.map((version) => (
                        <tr 
                          key={version.id} 
                          className={`hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer ${activeVersion?.id === version.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                          onClick={() => setActiveVersion(version)}
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {version.name}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {version.timestamp.toLocaleString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                              {version.clauses.length} clauses
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setActiveVersion(version);
                                setIsComparing(true);
                              }}
                              className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4"
                            >
                              Compare
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                if (window.confirm('Are you sure you want to delete this version?')) {
                                  setVersions(versions.filter(v => v.id !== version.id));
                                  if (activeVersion?.id === version.id) {
                                    setActiveVersion(null);
                                  }
                                }
                              }}
                              className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
        
        {activeTab === 'signatures' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-800 dark:text-white">Digital Signatures</h2>
              <button
                onClick={() => {
                  if (onFinalize) {
                    onFinalize();
                  } else {
                    alert('Contract finalized successfully!');
                  }
                }}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center"
                disabled={!signatures || signatures.length < 2}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Finalize Contract
              </button>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
            {/* First Party Signature */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-2">
                {formData.firstPartyName || 'First Party'} Signature
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                Please sign in the box below
              </p>

              {activeSigningParty === 'first' ? (
                <div className="mb-4">
                  <div
                    className="border-2 border-gray-300 dark:border-gray-600 rounded-lg h-40 w-full bg-white dark:bg-gray-800 cursor-crosshair"
                    onMouseDown={(e) => {
                      setIsDrawing(true);
                      const canvas = signatureCanvasRef.current;
                      if (!canvas) return;

                      const rect = canvas.getBoundingClientRect();
                      const x = e.clientX - rect.left;
                      const y = e.clientY - rect.top;
                      setLastPosition({ x, y });
                    }}
                    onMouseMove={(e) => {
                      if (!isDrawing || !lastPosition) return;

                      const canvas = signatureCanvasRef.current;
                      if (!canvas) return;

                      const ctx = canvas.getContext('2d');
                      if (!ctx) return;

                      const rect = canvas.getBoundingClientRect();
                      const x = e.clientX - rect.left;
                      const y = e.clientY - rect.top;

                      ctx.beginPath();
                      ctx.moveTo(lastPosition.x, lastPosition.y);
                      ctx.lineTo(x, y);
                      ctx.strokeStyle = '#000';
                      ctx.lineWidth = 2;
                      ctx.stroke();

                      setLastPosition({ x, y });
                    }}
                    onMouseUp={() => {
                      setIsDrawing(false);
                      setLastPosition(null);
                    }}
                    onMouseLeave={() => {
                      setIsDrawing(false);
                      setLastPosition(null);
                    }}
                  >
                    <canvas
                      ref={signatureCanvasRef}
                      width={500}
                      height={160}
                      className="w-full h-full"
                    />
                  </div>
                  <div className="flex justify-between mt-2">
                    <button
                      onClick={() => {
                        const canvas = signatureCanvasRef.current;
                        if (!canvas) return;

                        const ctx = canvas.getContext('2d');
                        if (!ctx) return;

                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                      }}
                      className="px-3 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                    >
                      Clear
                    </button>
                    <button
                      onClick={() => {
                        const canvas = signatureCanvasRef.current;
                        if (!canvas) return;

                        // Get signature as data URL
                        const signatureData = canvas.toDataURL('image/png');

                        // Create new signature object
                        const newSignature: Signature = {
                          party: 'first',
                          dateSigned: new Date(),
                          signatureData,
                        };

                        // Add to signatures array
                        setSignatures((prev) => [...prev, newSignature]);
                        setActiveSigningParty(null);
                      }}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                    >
                      Complete
                    </button>
                  </div>
                </div>
              ) : (
                <div className="mb-4">
                  {signatures?.some((s) => s.party === 'first') ? (
                    <div className="text-center p-4">
                      <p className="text-green-600 dark:text-green-400 font-medium mb-2">✓ Signed</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {signatures.find((s) => s.party === 'first')?.dateSigned?.toLocaleString()}
                      </p>
                    </div>
                  ) : (
                    <button
                      onClick={() => setActiveSigningParty('first')}
                      className="w-full py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center hover:border-blue-500 dark:hover:border-blue-500 transition-colors"
                    >
                      Click to sign
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Second Party Signature */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-2">
                {formData.secondPartyName || 'Second Party'} Signature
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                Please sign in the box below
              </p>

              {activeSigningParty === 'second' ? (
                <div className="mb-4">
                  <div
                    className="border-2 border-gray-300 dark:border-gray-600 rounded-lg h-40 w-full bg-white dark:bg-gray-800 cursor-crosshair"
                    onMouseDown={(e) => {
                      setIsDrawing(true);
                      const canvas = signatureCanvasRef.current;
                      if (!canvas) return;

                      const rect = canvas.getBoundingClientRect();
                      const x = e.clientX - rect.left;
                      const y = e.clientY - rect.top;
                      setLastPosition({ x, y });
                    }}
                    onMouseMove={(e) => {
                      if (!isDrawing || !lastPosition) return;

                      const canvas = signatureCanvasRef.current;
                      if (!canvas) return;

                      const ctx = canvas.getContext('2d');
                      if (!ctx) return;

                      const rect = canvas.getBoundingClientRect();
                      const x = e.clientX - rect.left;
                      const y = e.clientY - rect.top;

                      ctx.beginPath();
                      ctx.moveTo(lastPosition.x, lastPosition.y);
                      ctx.lineTo(x, y);
                      ctx.strokeStyle = '#000';
                      ctx.lineWidth = 2;
                      ctx.stroke();

                      setLastPosition({ x, y });
                    }}
                    onMouseUp={() => {
                      setIsDrawing(false);
                      setLastPosition(null);
                    }}
                    onMouseLeave={() => {
                      setIsDrawing(false);
                      setLastPosition(null);
                    }}
                  >
                    <canvas
                      ref={signatureCanvasRef}
                      width={500}
                      height={160}
                      className="w-full h-full"
                    />
                  </div>
                  <div className="flex justify-between mt-2">
                    <button
                      onClick={() => {
                        const canvas = signatureCanvasRef.current;
                        if (!canvas) return;

                        const ctx = canvas.getContext('2d');
                        if (!ctx) return;

                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                      }}
                      className="px-3 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                    >
                      Clear
                    </button>
                    <button
                      onClick={() => {
                        const canvas = signatureCanvasRef.current;
                        if (!canvas) return;

                        // Get signature as data URL
                        const signatureData = canvas.toDataURL('image/png');

                        // Create new signature object
                        const newSignature: Signature = {
                          party: 'second',
                          dateSigned: new Date(),
                          signatureData,
                        };

                        // Add to signatures array
                        setSignatures((prev) => [...prev, newSignature]);
                        setActiveSigningParty(null);
                      }}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                    >
                      Complete
                    </button>
                  </div>
                </div>
              ) : (
                <div className="mb-4">
                  {signatures?.some((s) => s.party === 'second') ? (
                    <div className="text-center p-4">
                      <p className="text-green-600 dark:text-green-400 font-medium mb-2">✓ Signed</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {signatures.find((s) => s.party === 'second')?.dateSigned?.toLocaleString()}
                      </p>
                    </div>
                  ) : (
                    <button
                      onClick={() => setActiveSigningParty('second')}
                      className="w-full py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center hover:border-blue-500 dark:hover:border-blue-500 transition-colors"
                    >
                      Click to sign
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-bold mb-4">Signature Status</h3>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                      signatures?.some((s) => s.party === 'first') ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    {signatures?.some((s) => s.party === 'first') ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    ) : (
                      <span className="text-white text-xs">1</span>
                    )}
                  </div>
                  <span>{formData.firstPartyName || 'First Party'}</span>
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {signatures?.some((s) => s.party === 'first') ? 'Signed' : 'Pending'}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                      signatures?.some((s) => s.party === 'second') ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    {signatures?.some((s) => s.party === 'second') ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    ) : (
                      <span className="text-white text-xs">2</span>
                    )}
                  </div>
                  <span>{formData.secondPartyName || 'Second Party'}</span>
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {signatures?.some((s) => s.party === 'second') ? 'Signed' : 'Pending'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};



