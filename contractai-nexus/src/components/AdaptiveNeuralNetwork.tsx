import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Contract type interface
export interface ContractType {
  id: string;
  name: string;
  description: string;
  color: string;
}

// Contract types data
export const contractTypes: ContractType[] = [
  {
    id: 'nda',
    name: 'Non-Disclosure',
    description: 'Protect your confidential information',
    color: '#38bdf8'
  },
  {
    id: 'employment',
    name: 'Employment',
    description: 'Hire with confidence',
    color: '#a78bfa'
  },
  {
    id: 'service',
    name: 'Service Agreement',
    description: 'Define your service terms',
    color: '#34d399'
  },
  {
    id: 'lease',
    name: 'Lease Agreement',
    description: 'Secure property arrangements',
    color: '#fb923c'
  },
  {
    id: 'partnership',
    name: 'Partnership',
    description: 'Structure your business relationships',
    color: '#f87171'
  },
  {
    id: 'custom',
    name: 'Custom Contract',
    description: 'Build from scratch',
    color: '#fbbf24'
  },
];

// Question and Answer Interfaces
interface Question {
  id: string;
  text: string;
  description?: string;
  options: QuestionOption[];
  category: string;
}

interface QuestionOption {
  id: string;
  text: string;
  description?: string;
  icon?: string;
  activatesHubs: string[];
  activatesContracts: string[];
  confidence: number;
}

// Network Node Interface (Enhanced)
interface AdaptiveNode {
  id: string;
  type: 'hub' | 'contract' | 'question';
  x: number;
  y: number;
  label: string;
  description: string;
  color: string;
  size: number;
  contractId?: string;
  isActive: boolean;
  confidence: number;
  connections: string[];
  category: string;
  pulseIntensity: number;
}

// User's Journey State
interface UserJourney {
  answers: Record<string, string>;
  activeHubs: Set<string>;
  activeContracts: Set<string>;
  confidenceScores: Record<string, number>;
  currentQuestion: number;
  isComplete: boolean;
}

// Decision Tree Questions
const adaptiveQuestions: Question[] = [
  {
    id: 'primary_goal',
    text: 'What\'s your primary business goal?',
    description: 'This helps us understand your main focus area',
    category: 'business_context',
    options: [
      {
        id: 'protect_information',
        text: 'Protect confidential information',
        icon: '🔒',
        description: 'Keep sensitive data and IP secure',
        activatesHubs: ['protect_ip'],
        activatesContracts: ['nda'],
        confidence: 0.8
      },
      {
        id: 'hire_people',
        text: 'Hire or work with people',
        icon: '👥',
        description: 'Bring talent into your organization',
        activatesHubs: ['hire_talent'],
        activatesContracts: ['employment', 'service'],
        confidence: 0.8
      },
      {
        id: 'business_operations',
        text: 'Manage business operations',
        icon: '⚙️',
        description: 'Handle day-to-day business relationships',
        activatesHubs: ['business_ops'],
        activatesContracts: ['service', 'lease'],
        confidence: 0.8
      },
      {
        id: 'grow_expand',
        text: 'Grow and expand business',
        icon: '📈',
        description: 'Scale operations and form partnerships',
        activatesHubs: ['growth_expansion'],
        activatesContracts: ['partnership', 'custom'],
        confidence: 0.8
      }
    ]
  },
  {
    id: 'relationship_type',
    text: 'What type of relationship are you creating?',
    description: 'This helps us narrow down the specific contract type',
    category: 'relationship_context',
    options: [
      {
        id: 'employee_relationship',
        text: 'Employee or team member',
        icon: '💼',
        description: 'Full-time, part-time, or contract employees',
        activatesHubs: ['hire_talent', 'protect_ip'],
        activatesContracts: ['employment', 'nda'],
        confidence: 0.9
      },
      {
        id: 'service_provider',
        text: 'Service provider or contractor',
        icon: '🔧',
        description: 'External contractors or service companies',
        activatesHubs: ['hire_talent', 'business_ops'],
        activatesContracts: ['service', 'nda'],
        confidence: 0.9
      },
      {
        id: 'business_partner',
        text: 'Business partner or investor',
        icon: '🤝',
        description: 'Strategic partnerships or investment relationships',
        activatesHubs: ['growth_expansion', 'protect_ip'],
        activatesContracts: ['partnership', 'nda'],
        confidence: 0.9
      },
      {
        id: 'property_tenant',
        text: 'Property or space arrangement',
        icon: '🏢',
        description: 'Leasing office space, equipment, or property',
        activatesHubs: ['business_ops', 'growth_expansion'],
        activatesContracts: ['lease'],
        confidence: 0.9
      },
      {
        id: 'custom_arrangement',
        text: 'Unique or complex arrangement',
        icon: '⚡',
        description: 'Something that doesn\'t fit standard categories',
        activatesHubs: ['growth_expansion', 'business_ops'],
        activatesContracts: ['custom'],
        confidence: 1.0
      }
    ]
  },
  {
    id: 'information_sensitivity',
    text: 'How sensitive is the information involved?',
    description: 'This determines the level of confidentiality protection needed',
    category: 'security_context',
    options: [
      {
        id: 'highly_sensitive',
        text: 'Highly sensitive (trade secrets, IP)',
        icon: '🔐',
        description: 'Critical business information that must be protected',
        activatesHubs: ['protect_ip'],
        activatesContracts: ['nda', 'employment'],
        confidence: 1.0
      },
      {
        id: 'moderately_sensitive',
        text: 'Moderately sensitive (business processes)',
        icon: '🔒',
        description: 'Important but not critical information',
        activatesHubs: ['protect_ip', 'business_ops'],
        activatesContracts: ['nda', 'service'],
        confidence: 0.7
      },
      {
        id: 'low_sensitivity',
        text: 'Low sensitivity (general business)',
        icon: '📄',
        description: 'Standard business information',
        activatesHubs: ['business_ops'],
        activatesContracts: ['service', 'lease'],
        confidence: 0.5
      },
      {
        id: 'no_sensitive_info',
        text: 'No sensitive information',
        icon: '📋',
        description: 'Public or non-confidential information only',
        activatesHubs: ['business_ops', 'growth_expansion'],
        activatesContracts: ['lease', 'partnership'],
        confidence: 0.3
      }
    ]
  }
];

// Business Hubs (Enhanced)
const adaptiveHubs: AdaptiveNode[] = [
  {
    id: 'protect_ip',
    type: 'hub',
    x: 200,
    y: 150,
    label: 'Protect IP',
    description: 'Safeguard intellectual property and confidential information',
    color: '#3b82f6',
    size: 50,
    isActive: false,
    confidence: 0,
    connections: ['nda', 'employment', 'partnership'],
    category: 'security',
    pulseIntensity: 0
  },
  {
    id: 'hire_talent',
    type: 'hub',
    x: 500,
    y: 100,
    label: 'Hire Talent',
    description: 'Bring people into your organization',
    color: '#10b981',
    size: 50,
    isActive: false,
    confidence: 0,
    connections: ['employment', 'service'],
    category: 'human_resources',
    pulseIntensity: 0
  },
  {
    id: 'business_ops',
    type: 'hub',
    x: 400,
    y: 300,
    label: 'Business Ops',
    description: 'Manage day-to-day business relationships',
    color: '#f59e0b',
    size: 50,
    isActive: false,
    confidence: 0,
    connections: ['service', 'lease', 'partnership'],
    category: 'operations',
    pulseIntensity: 0
  },
  {
    id: 'growth_expansion',
    type: 'hub',
    x: 150,
    y: 350,
    label: 'Growth',
    description: 'Scale and expand your business',
    color: '#8b5cf6',
    size: 50,
    isActive: false,
    confidence: 0,
    connections: ['partnership', 'lease', 'custom'],
    category: 'growth',
    pulseIntensity: 0
  }
];

// Create Contract Nodes
const createAdaptiveContractNodes = (): AdaptiveNode[] => {
  return contractTypes.map((contract: ContractType, index: number) => {
    const angle = (index / contractTypes.length) * 2 * Math.PI;
    const radius = 180;
    const centerX = 350;
    const centerY = 225;
    
    return {
      id: contract.id,
      type: 'contract',
      x: centerX + Math.cos(angle) * radius,
      y: centerY + Math.sin(angle) * radius,
      label: contract.name,
      description: contract.description,
      color: contract.color,
      size: 35,
      contractId: contract.id,
      isActive: false,
      confidence: 0,
      connections: getContractConnections(contract.id),
      category: 'contract',
      pulseIntensity: 0
    };
  });
};

// Get contract connections
const getContractConnections = (contractId: string): string[] => {
  const connections: Record<string, string[]> = {
    'nda': ['protect_ip', 'hire_talent'],
    'employment': ['hire_talent', 'protect_ip'],
    'service': ['hire_talent', 'business_ops'],
    'lease': ['business_ops', 'growth_expansion'],
    'partnership': ['protect_ip', 'business_ops', 'growth_expansion'],
    'custom': ['growth_expansion', 'business_ops']
  };
  return connections[contractId] || [];
};

// Main Adaptive Neural Network Component
interface AdaptiveNeuralNetworkProps {
  onSelectContract: (contract: ContractType | null) => void;
  className?: string;
}

const AdaptiveNeuralNetwork: React.FC<AdaptiveNeuralNetworkProps> = ({
  onSelectContract,
  className = ''
}) => {
  const [nodes, setNodes] = useState<AdaptiveNode[]>([...adaptiveHubs, ...createAdaptiveContractNodes()]);
  const [userJourney, setUserJourney] = useState<UserJourney>({
    answers: {},
    activeHubs: new Set(),
    activeContracts: new Set(),
    confidenceScores: {},
    currentQuestion: 0,
    isComplete: false
  });
  const [selectedContract, setSelectedContract] = useState<ContractType | null>(null);
  const [showRecommendations, setShowRecommendations] = useState(false);
  const animationRef = useRef<number | null>(null);

  // Animation loop for dynamic effects
  useEffect(() => {
    const animate = () => {
      setNodes(prevNodes => prevNodes.map(node => ({
        ...node,
        pulseIntensity: node.isActive ? 
          Math.sin(Date.now() * 0.003 + parseInt(node.id, 36)) * 0.3 + 0.7 : 0
      })));
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Handle question answers
  const handleAnswerSelect = (questionId: string, optionId: string) => {
    const question = adaptiveQuestions[userJourney.currentQuestion];
    const option = question.options.find(opt => opt.id === optionId);
    
    if (!option) return;

    // Update user journey
    const newJourney: UserJourney = {
      ...userJourney,
      answers: { ...userJourney.answers, [questionId]: optionId },
      activeHubs: new Set([...Array.from(userJourney.activeHubs), ...option.activatesHubs]),
      activeContracts: new Set([...Array.from(userJourney.activeContracts), ...option.activatesContracts]),
      currentQuestion: userJourney.currentQuestion + 1,
      isComplete: userJourney.currentQuestion >= adaptiveQuestions.length - 1
    };

    // Update confidence scores
    option.activatesContracts.forEach(contractId => {
      newJourney.confidenceScores[contractId] = 
        (newJourney.confidenceScores[contractId] || 0) + option.confidence;
    });

    setUserJourney(newJourney);

    // Update node states
    setNodes(prevNodes => prevNodes.map(node => {
      const isHub = node.type === 'hub' && newJourney.activeHubs.has(node.id);
      const isContract = node.type === 'contract' && newJourney.activeContracts.has(node.id);
      const confidence = node.type === 'contract' ? 
        (newJourney.confidenceScores[node.id] || 0) : 
        (isHub ? 1 : 0);

      return {
        ...node,
        isActive: isHub || isContract,
        confidence: Math.min(confidence, 1),
        size: node.type === 'hub' ? 
          (isHub ? 60 : 40) : 
          (isContract ? 45 : 25)
      };
    }));

    // Show recommendations if journey is complete
    if (newJourney.isComplete) {
      setShowRecommendations(true);
    }
  };

  // Handle contract selection
  const handleContractSelect = (node: AdaptiveNode) => {
    if (node.type === 'contract' && node.contractId && node.isActive) {
      const contract = contractTypes.find((c: ContractType) => c.id === node.contractId);
      if (contract) {
        setSelectedContract(contract);
        onSelectContract(contract);
      }
    }
  };

  // Reset the journey
  const resetJourney = () => {
    setUserJourney({
      answers: {},
      activeHubs: new Set(),
      activeContracts: new Set(),
      confidenceScores: {},
      currentQuestion: 0,
      isComplete: false
    });
    setSelectedContract(null);
    setShowRecommendations(false);
    setNodes(prevNodes => prevNodes.map(node => ({
      ...node,
      isActive: false,
      confidence: 0,
      size: node.type === 'hub' ? 50 : 35,
      pulseIntensity: 0
    })));
  };

  const currentQuestion = adaptiveQuestions[userJourney.currentQuestion];
  const recommendedContracts = Object.entries(userJourney.confidenceScores)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([contractId]) => contractTypes.find(c => c.id === contractId))
    .filter(Boolean) as ContractType[];

  return (
    <div className={`w-full ${className}`}>
      {/* Progress Indicator */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span>Building Your Personalized Network</span>
          <span>{userJourney.currentQuestion + 1} / {adaptiveQuestions.length}</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-dark-300 rounded-full h-2">
          <motion.div 
            className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((userJourney.currentQuestion + 1) / adaptiveQuestions.length) * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Question Panel */}
        <div className="space-y-6">
          {!userJourney.isComplete && currentQuestion && (
            <motion.div
              key={currentQuestion.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white dark:bg-dark-200 rounded-xl shadow-lg p-6"
            >
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {currentQuestion.text}
              </h3>
              {currentQuestion.description && (
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {currentQuestion.description}
                </p>
              )}
              
              <div className="space-y-3">
                {currentQuestion.options.map((option, index) => (
                  <motion.button
                    key={option.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => handleAnswerSelect(currentQuestion.id, option.id)}
                    className="w-full p-4 text-left bg-gray-50 dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-100 transition-all duration-200 group"
                  >
                    <div className="flex items-start space-x-3">
                      {option.icon && (
                        <span className="text-2xl group-hover:scale-110 transition-transform duration-200">
                          {option.icon}
                        </span>
                      )}
                      <div className="flex-1">
                        <div className="font-semibold text-gray-900 dark:text-white mb-1">
                          {option.text}
                        </div>
                        {option.description && (
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {option.description}
                          </div>
                        )}
                      </div>
                      <div className="text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
                        →
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Recommendations Panel */}
          <AnimatePresence>
            {showRecommendations && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-dark-200 rounded-xl shadow-lg p-6"
              >
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  🎯 Your Personalized Recommendations
                </h3>
                
                <div className="space-y-3">
                  {recommendedContracts.map((contract, index) => (
                    <motion.div
                      key={contract.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                        selectedContract?.id === contract.id
                          ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-gray-200 dark:border-dark-300 hover:border-primary-300'
                      }`}
                      onClick={() => {
                        setSelectedContract(contract);
                        onSelectContract(contract);
                      }}
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold"
                          style={{ backgroundColor: contract.color }}
                        >
                          {contract.name.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {contract.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {Math.round((userJourney.confidenceScores[contract.id] || 0) * 100)}% match
                          </div>
                        </div>
                        {selectedContract?.id === contract.id && (
                          <div className="text-primary-600 dark:text-primary-400">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>

                <button
                  onClick={resetJourney}
                  className="mt-4 w-full px-4 py-2 bg-gray-200 dark:bg-dark-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-dark-100 transition-colors duration-200"
                >
                  Start Over
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Adaptive Network Visualization */}
        <div className="bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20 dark:from-gray-950 dark:via-blue-950/30 dark:to-purple-950/30 rounded-xl overflow-hidden border border-gray-700/50" style={{ height: '500px' }}>
          <div className="relative w-full h-full">
            {/* Enhanced Background with Animated Grid */}
            <div className="absolute inset-0">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="adaptive-grid" width="30" height="30" patternUnits="userSpaceOnUse">
                    <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#374151" strokeWidth="0.3" opacity="0.4"/>
                    <circle cx="0" cy="0" r="1" fill="#60a5fa" opacity="0.2"/>
                  </pattern>
                  <radialGradient id="nodeGlow" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8"/>
                    <stop offset="100%" stopColor="#3b82f6" stopOpacity="0"/>
                  </radialGradient>
                  <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8"/>
                    <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.6"/>
                    <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.8"/>
                  </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#adaptive-grid)" />
                {/* Ambient glow effects */}
                <circle cx="20%" cy="30%" r="100" fill="url(#nodeGlow)" opacity="0.1"/>
                <circle cx="80%" cy="70%" r="80" fill="url(#nodeGlow)" opacity="0.08"/>
              </svg>
            </div>

            {/* Enhanced Connections */}
            <svg className="absolute inset-0 w-full h-full">
              {nodes.map(node => 
                node.connections.map(targetId => {
                  const targetNode = nodes.find(n => n.id === targetId);
                  if (!targetNode) return null;
                  
                  const isActive = node.isActive && targetNode.isActive;
                  const connectionId = `${node.id}-${targetId}`;
                  
                  return (
                    <g key={connectionId}>
                      {/* Glow effect for active connections */}
                      {isActive && (
                        <motion.line
                          x1={node.x}
                          y1={node.y}
                          x2={targetNode.x}
                          y2={targetNode.y}
                          stroke="url(#connectionGradient)"
                          strokeWidth="8"
                          strokeOpacity="0.3"
                          filter="blur(2px)"
                          initial={{ pathLength: 0 }}
                          animate={{ pathLength: 1 }}
                          transition={{ duration: 1.2, delay: 0.2 }}
                        />
                      )}
                      {/* Main connection line */}
                      <motion.line
                        x1={node.x}
                        y1={node.y}
                        x2={targetNode.x}
                        y2={targetNode.y}
                        stroke={isActive ? 'url(#connectionGradient)' : '#4b5563'}
                        strokeWidth={isActive ? 2.5 : 1}
                        strokeOpacity={isActive ? 0.9 : 0.3}
                        strokeDasharray={isActive ? '0' : '5,5'}
                        initial={{ pathLength: 0 }}
                        animate={{ 
                          pathLength: 1,
                          strokeOpacity: isActive ? 0.9 : 0.3,
                          strokeWidth: isActive ? 2.5 : 1
                        }}
                        transition={{ duration: 0.8 }}
                      />
                      {/* Animated pulse for active connections */}
                      {isActive && (
                        <motion.circle
                          r="3"
                          fill="#60a5fa"
                          opacity="0.8"
                          initial={{ 
                            cx: node.x, 
                            cy: node.y 
                          }}
                          animate={{
                            cx: [node.x, targetNode.x],
                            cy: [node.y, targetNode.y]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "linear"
                          }}
                        />
                      )}
                    </g>
                  );
                })
              )}
            </svg>

            {/* Enhanced Nodes */}
            {nodes.map((node) => (
              <motion.div
                key={node.id}
                className="absolute cursor-pointer group"
                style={{
                  left: node.x - node.size / 2,
                  top: node.y - node.size / 2,
                  width: node.size,
                  height: node.size,
                }}
                initial={{ scale: 0, opacity: 0.3 }}
                animate={{ 
                  scale: 1, 
                  opacity: node.isActive ? 1 : 0.4,
                }}
                transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
                whileHover={{ scale: node.isActive ? 1.3 : 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleContractSelect(node)}
              >
                {/* Outer glow ring for active nodes */}
                {node.isActive && (
                  <motion.div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: `radial-gradient(circle, ${node.color}40 0%, transparent 70%)`,
                      transform: 'scale(2)'
                    }}
                    animate={{
                      opacity: [0.3, 0.7, 0.3],
                      scale: [1.8, 2.2, 1.8]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
                
                {/* Main node */}
                <div
                  className={`w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xs transition-all duration-300 border-2 ${
                    selectedContract?.id === node.contractId 
                      ? 'ring-4 ring-white shadow-lg' 
                      : node.isActive 
                        ? 'border-white/30 shadow-lg' 
                        : 'border-gray-600/50'
                  }`}
                  style={{ 
                    background: node.isActive 
                      ? `linear-gradient(135deg, ${node.color}, ${node.color}dd)` 
                      : `${node.color}60`,
                    boxShadow: node.isActive 
                      ? `0 0 20px ${node.color}80, inset 0 1px 0 rgba(255,255,255,0.2)` 
                      : 'none',
                    transform: `scale(${1 + node.pulseIntensity * 0.1})`
                  }}
                >
                  {/* Node icon/text with enhanced styling */}
                  <span className={`${node.isActive ? 'drop-shadow-sm' : ''} transition-all duration-200`}>
                    {node.type === 'hub' ? '🧠' : (
                      <span className="text-sm font-extrabold tracking-wider">
                        {node.label.charAt(0)}
                      </span>
                    )}
                  </span>
                </div>

                {/* Enhanced Confidence Indicator */}
                {node.type === 'contract' && node.confidence > 0 && (
                  <motion.div 
                    className="absolute -top-2 -right-2 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs rounded-full w-7 h-7 flex items-center justify-center font-bold border-2 border-white shadow-lg"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: "spring", stiffness: 300 }}
                  >
                    {Math.round(node.confidence * 100)}
                  </motion.div>
                )}

                {/* Enhanced Node Label */}
                <motion.div 
                  className="absolute top-full left-1/2 transform -translate-x-1/2 mt-3 text-xs text-center whitespace-nowrap px-2 py-1 rounded-md transition-all duration-200"
                  style={{
                    color: node.isActive ? '#e5e7eb' : '#9ca3af',
                    backgroundColor: node.isActive ? 'rgba(0,0,0,0.6)' : 'rgba(0,0,0,0.3)',
                    backdropFilter: 'blur(4px)',
                    fontWeight: node.isActive ? '600' : '400'
                  }}
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  {node.label}
                </motion.div>

                {/* Pulse effect for highly active nodes */}
                {node.pulseIntensity > 0.5 && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2"
                    style={{ borderColor: node.color }}
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.8, 0, 0.8]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeOut"
                    }}
                  />
                )}
              </motion.div>
            ))}

            {/* Instructions */}
            <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white text-sm">
              <p className="font-medium mb-1">🧠 Adaptive Network</p>
              <p className="text-xs opacity-80">Answer questions to build your network</p>
              <p className="text-xs opacity-80">Active nodes show your personalized path</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdaptiveNeuralNetwork;
