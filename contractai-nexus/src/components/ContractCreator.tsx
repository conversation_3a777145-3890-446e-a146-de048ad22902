import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import ContractGalaxy, { ContractType } from './ContractGalaxy';
import EnhancedSmartForm from './EnhancedSmartForm';
import EnhancedAIClauseBuilder from './EnhancedAIClauseBuilder';
import { Clause } from '../types/clauses';
import ContractPreview from './ContractPreview';
import AIAssistant from './AIAssistant';
import CollaborationPanel from './CollaborationPanel';
import CollaborationService from '../services/CollaborationService';
import ProgressTracker, { CreationStage } from './ProgressTracker';
import SimpleProgressBar, { SimpleStage } from './SimpleProgressBar';
import ExpressModeToggle from './ExpressModeToggle';
import SmartTemplates from './SmartTemplates';
import { useExpressMode } from '../contexts/ExpressModeContext';
import { ContractTemplate } from '../data/contractTemplates';
import { getAutoFillDefaults } from '../utils/autoFillDefaults';

const ContractCreator: React.FC = () => {
  const { isExpressMode } = useExpressMode();
  const [stage, setStage] = useState<CreationStage>(CreationStage.SELECT_TYPE);
  const [selectedContract, setSelectedContract] = useState<ContractType | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<ContractTemplate | null>(null);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [clauses, setClauses] = useState<Clause[]>([]);
  const [isAssistantOpen, setIsAssistantOpen] = useState<boolean>(false);
  const [isCollaborationOpen, setIsCollaborationOpen] = useState<boolean>(false);
  const [contractId] = useState<string>(uuidv4());
  const [collaborationEnabled, setCollaborationEnabled] = useState<boolean>(false);
  const [collaboratorsCount, setCollaboratorsCount] = useState<number>(0);
  const [showTemplates, setShowTemplates] = useState<boolean>(true);
  
  const handleContractSelect = (contract: ContractType | null) => {
    if (contract) {
      setSelectedContract(contract);
      setShowTemplates(false);
      setStage(CreationStage.FILL_FORM);
    }
  };

  const handleTemplateSelect = (template: ContractTemplate) => {
    setSelectedTemplate(template);
    setSelectedContract(template.contractType);
    setShowTemplates(false);
    
    // Auto-fill form data with template defaults and smart defaults
    const autoFillData = getAutoFillDefaults(
      template.contractType,
      undefined, // No user profile for now
      template.defaultData
    );
    setFormData(autoFillData);
    
    // In express mode, skip directly to clauses if minimal fields required
    if (isExpressMode && template.requiredFields.length <= 3) {
      setStage(CreationStage.BUILD_CLAUSES);
    } else {
      setStage(CreationStage.FILL_FORM);
    }
  };
  
  const handleFormComplete = (data: Record<string, any>) => {
    setFormData(data);
    setStage(CreationStage.BUILD_CLAUSES);
    
    // Update collaboration service if enabled
    if (collaborationEnabled) {
      const collaborationService = CollaborationService.getInstance();
      collaborationService.updateFormData(contractId, data);
    }
  };
  
  const handleAddClause = (clause: Clause) => {
    setClauses((prev) => {
      // Check if clause already exists
      if (prev.some((c) => c.id === clause.id)) {
        return prev;
      }
      const newClauses = [...prev, clause];
      
      // Update collaboration service if enabled
      if (collaborationEnabled) {
        const collaborationService = CollaborationService.getInstance();
        collaborationService.updateClauses(contractId, newClauses);
      }
      
      return newClauses;
    });
  };
  
  const handleRemoveClause = (clauseId: string) => {
    setClauses((prev) => {
      const newClauses = prev.filter((clause) => clause.id !== clauseId);
      
      // Update collaboration service if enabled
      if (collaborationEnabled) {
        const collaborationService = CollaborationService.getInstance();
        collaborationService.updateClauses(contractId, newClauses);
      }
      
      return newClauses;
    });
  };

  const handleReorderClauses = (reorderedClauses: Clause[]) => {
    setClauses(reorderedClauses);
    
    // Update collaboration service if enabled
    if (collaborationEnabled) {
      const collaborationService = CollaborationService.getInstance();
      collaborationService.updateClauses(contractId, reorderedClauses);
    }
  };
  
  const handleClausesBuildingComplete = () => {
    setStage(CreationStage.PREVIEW);
  };
  
  const handleBackToSelection = () => {
    setStage(CreationStage.SELECT_TYPE);
    setSelectedContract(null);
    setFormData({});
    setClauses([]);
  };
  
  const handleBackToForm = () => {
    setStage(CreationStage.FILL_FORM);
  };
  
  const handleBackToClauses = () => {
    setStage(CreationStage.BUILD_CLAUSES);
  };
  
  const toggleAssistant = () => {
    setIsAssistantOpen(!isAssistantOpen);
  };
  
  const toggleCollaboration = () => {
    setIsCollaborationOpen(!isCollaborationOpen);
  };
  
  const enableCollaboration = () => {
    if (!collaborationEnabled) {
      setCollaborationEnabled(true);
      
      const collaborationService = CollaborationService.getInstance();
      const session = collaborationService.joinSession(
        contractId,
        selectedContract,
        formData,
        clauses
      );
      
      setCollaboratorsCount(session.users.length);
    }
  };
  
  const handleStageClick = (newStage: CreationStage) => {
    // Only allow navigation to stages that have been previously visited
    if (newStage <= stage) {
      setStage(newStage);
    }
  };

  const handleFinalize = () => {
    // Handle contract finalization
    console.log('Contract finalized:', {
      contractType: selectedContract,
      formData,
      clauses
    });
    // In a real app, this would save to database, send for signatures, etc.
    alert('Contract has been finalized successfully!');
  };

  
  const renderProgressTracker = () => {
    // Convert CreationStage to SimpleStage
    const getSimpleStage = (creationStage: CreationStage): SimpleStage => {
      switch (creationStage) {
        case CreationStage.SELECT_TYPE: return SimpleStage.SELECT_TYPE;
        case CreationStage.FILL_FORM: return SimpleStage.FILL_FORM;
        case CreationStage.BUILD_CLAUSES: return SimpleStage.BUILD_CLAUSES;
        case CreationStage.PREVIEW: return SimpleStage.PREVIEW_SIGN;
        default: return SimpleStage.SELECT_TYPE;
      }
    };

    return isExpressMode ? (
      <SimpleProgressBar 
        currentStage={getSimpleStage(stage)} 
        isExpressMode={true}
      />
    ) : (
      <ProgressTracker 
        currentStage={stage} 
        onStageClick={handleStageClick}
        allowNavigation={true}
        className="mb-8"
      />
    );
  };
  
  const renderStageContent = () => {
    switch (stage) {
      case CreationStage.SELECT_TYPE:
        return (
          <div className="w-full max-w-6xl mx-auto px-4">
            {/* Express Mode Toggle */}
            <div className="flex justify-center mb-6">
              <ExpressModeToggle />
            </div>

            {/* Header Section */}
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {isExpressMode ? 'Quick Contract Creation' : 'Choose Your Contract Type'}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                {isExpressMode 
                  ? 'Get started in minutes with our popular contract templates'
                  : 'Select the type of contract you want to create. Each contract type comes with pre-built templates and smart suggestions tailored to your needs.'
                }
              </p>
            </div>

            {/* Smart Templates (Express Mode) or Contract Galaxy */}
            {showTemplates && isExpressMode ? (
              <div className="bg-white dark:bg-dark-200 rounded-2xl shadow-xl p-6 md:p-8 mb-8">
                <SmartTemplates onTemplateSelect={handleTemplateSelect} />
                <div className="mt-6 text-center">
                  <button
                    onClick={() => setShowTemplates(false)}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors"
                  >
                    Or browse all contract types →
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-white dark:bg-dark-200 rounded-2xl shadow-xl p-6 md:p-8">
                <ContractGalaxy onSelectContract={handleContractSelect} />
                {isExpressMode && (
                  <div className="mt-6 text-center">
                    <button
                      onClick={() => setShowTemplates(true)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors"
                    >
                      ← Back to Quick Templates
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Help Section */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                <div className="text-2xl mb-2">🎯</div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">Purpose-Built</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Each contract type includes industry-specific clauses and terms
                </p>
              </div>
              <div className="text-center p-4 bg-secondary-50 dark:bg-secondary-900/20 rounded-lg">
                <div className="text-2xl mb-2">⚡</div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">AI-Powered</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Smart suggestions and automated clause generation
                </p>
              </div>
              <div className="text-center p-4 bg-accent-50 dark:bg-accent-900/20 rounded-lg">
                <div className="text-2xl mb-2">🔒</div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">Legally Sound</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  All templates are reviewed by legal professionals
                </p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                💡 <strong>Tip:</strong> Not sure which contract type to choose? 
                Start with "Custom Contract" to build from scratch with AI assistance.
              </p>
            </div>
          </div>
        );
      
      case CreationStage.FILL_FORM:
        return (
          <div className="w-full max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
              Fill Contract Details
            </h2>
            <EnhancedSmartForm
              contractType={selectedContract}
              onComplete={handleFormComplete}
              onBack={handleBackToSelection}
            />
          </div>
        );
      
      case CreationStage.BUILD_CLAUSES:
        return (
          <div className="w-full max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
              Build Contract Clauses
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <EnhancedAIClauseBuilder
                contractType={selectedContract}
                formData={formData}
                onAddClause={handleAddClause}
                onRemoveClause={handleRemoveClause}
                onReorderClauses={handleReorderClauses}
                existingClauses={clauses}
              />
              
              <div className="bg-white dark:bg-dark-200 rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  Selected Clauses ({clauses.length})
                </h3>
                
                {clauses.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <p className="text-gray-500 dark:text-gray-400">
                      No clauses added yet. Browse or create custom clauses on the left.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
                    {clauses.map((clause) => (
                      <div
                        key={clause.id}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {clause.title}
                          </h4>
                          <button
                            onClick={() => handleRemoveClause(clause.id)}
                            className="text-red-500 hover:text-red-700 dark:hover:text-red-400"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                          {clause.content}
                        </p>
                        <div className="flex items-center text-xs">
                          <span className="px-2 py-1 rounded-full bg-gray-100 dark:bg-dark-400 text-gray-800 dark:text-gray-300">
                            {clause.complexity}
                          </span>
                          {clause.isCustom && (
                            <span className="ml-2 px-2 py-1 rounded-full bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200">
                              Custom
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                <div className="flex justify-between mt-6">
                  <button
                    onClick={handleBackToForm}
                    className="px-6 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-dark-300 dark:hover:bg-dark-400 text-gray-800 dark:text-white rounded-lg transition-colors"
                  >
                    Back
                  </button>
                  <button
                    onClick={handleClausesBuildingComplete}
                    className="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                  >
                    Continue to Preview
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case CreationStage.PREVIEW:
        return (
          <div className="w-full max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Contract Preview
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  Review your {selectedContract?.name} contract before finalizing
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setStage(CreationStage.BUILD_CLAUSES)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  ← Back
                </button>
                <button
                  onClick={handleFinalize}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                >
                  Finalize Contract
                </button>
              </div>
            </div>
            <ContractPreview
              contractType={selectedContract}
              formData={formData}
              clauses={clauses}
            />
          </div>
        );
      
      default:
        return null;
    }
  };
  
  // Effect to subscribe to collaboration updates
  useEffect(() => {
    if (!collaborationEnabled) return;
    
    const collaborationService = CollaborationService.getInstance();
    
    // Subscribe to updates
    const unsubscribe = collaborationService.subscribeToUpdates(contractId, (update) => {
      // Skip updates from the current user
      const currentUser = collaborationService.getCurrentUser();
      if (currentUser && update.userId === currentUser.id) return;
      
      // Handle different update types
      switch (update.type) {
        case 'form':
          setFormData(prev => ({ ...prev, ...update.data }));
          break;
        case 'clause':
          setClauses(update.data);
          break;
        case 'user_joined':
        case 'user_left':
          const session = collaborationService.getSession(contractId);
          if (session) {
            setCollaboratorsCount(session.users.filter(u => u.isActive).length);
          }
          break;
      }
    });
    
    // Cleanup subscription when component unmounts
    return () => {
      unsubscribe();
      if (collaborationEnabled) {
        collaborationService.leaveSession(contractId);
      }
    };
  }, [collaborationEnabled, contractId]);
  
  return (
    <div className="relative min-h-screen bg-gray-50 dark:bg-dark-100 p-6">
      {/* Progress tracker */}
      {renderProgressTracker()}
      
      {/* Main content */}
      {renderStageContent()}
      
      {/* Collaboration button */}
      <div className="fixed bottom-6 right-6 flex flex-col space-y-4 z-40">
        {/* Collaboration toggle button */}
        <button
          onClick={() => {
            toggleCollaboration();
            if (!collaborationEnabled) {
              enableCollaboration();
            }
          }}
          className="p-4 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full shadow-lg transition-colors relative"
          aria-label="Toggle Collaboration"
        >
          {isCollaborationOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {collaborationEnabled && collaboratorsCount > 1 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {collaboratorsCount - 1}
                </span>
              )}
            </>
          )}
        </button>
        
        {/* AI Assistant toggle button */}
        <button
          onClick={toggleAssistant}
          className="p-4 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-colors"
          aria-label="Toggle AI Assistant"
        >
          {isAssistantOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          )}
        </button>
      </div>
      
      {/* AI Assistant panel */}
      <AIAssistant
        contractType={selectedContract}
        formData={formData}
        clauses={clauses}
        isOpen={isAssistantOpen}
        onClose={() => setIsAssistantOpen(false)}
      />
      
      {/* Collaboration panel */}
      <CollaborationPanel
        contractId={contractId}
        isOpen={isCollaborationOpen}
        onClose={() => setIsCollaborationOpen(false)}
      />
    </div>
  );
};

export default ContractCreator;
