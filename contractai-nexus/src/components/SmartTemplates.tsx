import React from 'react';
import { motion } from 'framer-motion';
import { Clock, Star, ArrowRight } from 'lucide-react';
import { contractTemplates, getTemplatesByPopularity, ContractTemplate } from '../data/contractTemplates';
import { ContractType } from './ContractGalaxy';

interface SmartTemplatesProps {
  onTemplateSelect: (template: ContractTemplate) => void;
  showAll?: boolean;
}

const SmartTemplates: React.FC<SmartTemplatesProps> = ({ onTemplateSelect, showAll = false }) => {
  const templates = showAll ? contractTemplates : getTemplatesByPopularity();

  const handleTemplateClick = (template: ContractTemplate) => {
    onTemplateSelect(template);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {showAll ? 'All Contract Templates' : 'Quick Start Templates'}
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          {showAll 
            ? 'Choose from our complete library of contract templates'
            : 'Get started in minutes with our most popular templates'
          }
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template, index) => (
          <motion.div
            key={template.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="group"
          >
            <motion.button
              onClick={() => handleTemplateClick(template)}
              className="w-full p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 text-left group-hover:shadow-lg"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{template.icon}</div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {template.name}
                    </h4>
                    {template.isPopular && (
                      <div className="flex items-center space-x-1 mt-1">
                        <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                          Popular
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors opacity-0 group-hover:opacity-100" />
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                {template.description}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {template.estimatedTime}
                  </span>
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                  {template.requiredFields.length} fields
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
                <div className="flex flex-wrap gap-2">
                  {template.requiredFields.slice(0, 3).map((field) => (
                    <span
                      key={field}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-400 rounded"
                    >
                      {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </span>
                  ))}
                  {template.requiredFields.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-400 rounded">
                      +{template.requiredFields.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </motion.button>
          </motion.div>
        ))}
      </div>

      {!showAll && (
        <div className="text-center">
          <motion.button
            className="px-6 py-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Templates →
          </motion.button>
        </div>
      )}
    </div>
  );
};

export default SmartTemplates;
