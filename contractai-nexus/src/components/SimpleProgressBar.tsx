import React from 'react';
import { motion } from 'framer-motion';
import { Check } from 'lucide-react';

export enum SimpleStage {
  SELECT_TYPE = 0,
  FILL_FORM = 1,
  BUILD_CLAUSES = 2,
  PREVIEW_SIGN = 3,
}

interface SimpleProgressBarProps {
  currentStage: SimpleStage;
  isExpressMode?: boolean;
}

const stageLabels = {
  [SimpleStage.SELECT_TYPE]: 'Select Contract',
  [SimpleStage.FILL_FORM]: 'Fill Details',
  [SimpleStage.BUILD_CLAUSES]: 'Review Terms',
  [SimpleStage.PREVIEW_SIGN]: 'Finalize',
};

const expressStageLabels = {
  [SimpleStage.SELECT_TYPE]: 'Choose Template',
  [SimpleStage.FILL_FORM]: 'Quick Details',
  [SimpleStage.BUILD_CLAUSES]: 'Review',
  [SimpleStage.PREVIEW_SIGN]: 'Complete',
};

const SimpleProgressBar: React.FC<SimpleProgressBarProps> = ({ 
  currentStage, 
  isExpressMode = false 
}) => {
  const totalStages = 4;
  const progress = ((currentStage + 1) / totalStages) * 100;
  const labels = isExpressMode ? expressStageLabels : stageLabels;

  return (
    <div className="w-full max-w-xl mx-auto mb-8">
      {/* Progress Bar */}
      <div className="relative mb-4">
        <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
        
        {/* Progress Percentage */}
        <div className="absolute -top-8 left-0 right-0 flex justify-center">
          <motion.div
            className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-2.5 py-0.5 rounded-full text-xs font-medium shadow-sm"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3 }}
          >
            {Math.round(progress)}%
          </motion.div>
        </div>
      </div>

      {/* Stage Indicators */}
      <div className="max-w-md mx-auto flex justify-between items-center">
        {Object.entries(labels).map(([stage, label]) => {
          const stageNum = parseInt(stage);
          const isCompleted = stageNum < currentStage;
          const isCurrent = stageNum === currentStage;
          
          return (
            <div key={stage} className="flex flex-col items-center space-y-2">
              <motion.div
                className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 ring-1 ${
                  isCompleted
                    ? 'bg-green-50 dark:bg-green-900/20 ring-green-500 text-green-600'
                    : isCurrent
                    ? 'bg-indigo-50 dark:bg-indigo-900/20 ring-indigo-500 text-indigo-600'
                    : 'bg-transparent ring-gray-300 dark:ring-gray-700 text-gray-400'
                }`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: stageNum * 0.1 }}
              >
                {isCompleted ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <span className="text-[11px] font-medium">{stageNum + 1}</span>
                )}
              </motion.div>
              
              <motion.span
                className={`text-[11px] sm:text-xs font-medium text-center max-w-24 ${
                  isCurrent
                    ? 'text-indigo-600 dark:text-indigo-400'
                    : isCompleted
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 dark:text-gray-400'
                }`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: stageNum * 0.1 + 0.2 }}
              >
                {label}
              </motion.span>
            </div>
          );
        })}
      </div>

      {/* Current Stage Description */}
      <motion.div
        className="text-center mt-3"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <p className="text-xs text-gray-600 dark:text-gray-400 tracking-wide">
          {isExpressMode ? 'Express Mode · ' : ''}
          Step {currentStage + 1} of {totalStages} · {labels[currentStage]}
        </p>
      </motion.div>
    </div>
  );
};

export default SimpleProgressBar;
