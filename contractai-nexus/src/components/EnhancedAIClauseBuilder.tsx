import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { ChevronUp, ChevronDown, X } from 'lucide-react';
import { ContractType } from './ContractGalaxy';
import { Clause } from '../types/clauses';
import SearchInput from './SearchInput';

// Clause type now comes from shared src/types/clauses

interface ClauseTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  contractTypes: string[];
  template: string;
  complexity: 'simple' | 'standard' | 'complex';
  isRequired?: boolean;
  riskLevel?: 'low' | 'medium' | 'high';
}

interface AIClauseBuilderProps {
  contractType: ContractType | null;
  formData: Record<string, any>;
  onAddClause: (clause: Clause) => void;
  onRemoveClause?: (clauseId: string) => void;
  onReorderClauses?: (reorderedClauses: Clause[]) => void;
  existingClauses: Clause[];
  className?: string;
}

// Enhanced clause templates
const clauseTemplates: ClauseTemplate[] = [
  {
    id: 'nda-definition',
    title: 'Confidential Information Definition',
    description: 'Defines what constitutes confidential information',
    category: 'definitions',
    contractTypes: ['nda'],
    template: `"Confidential Information" means any non-public information disclosed by one party to the other, including technical data, trade secrets, business information, and proprietary materials.`,
    complexity: 'standard',
    isRequired: true,
    riskLevel: 'medium'
  },
  {
    id: 'employment-duties',
    title: 'Job Duties and Responsibilities',
    description: 'Defines the employee\'s role and responsibilities',
    category: 'duties',
    contractTypes: ['employment'],
    template: `Employee shall serve in the position of [Job Title] and perform duties customarily associated with such position, devoting full business time and attention to the performance of duties.`,
    complexity: 'simple',
    isRequired: true,
    riskLevel: 'low'
  },
  {
    id: 'service-scope',
    title: 'Scope of Services',
    description: 'Defines the services to be provided',
    category: 'scope',
    contractTypes: ['service'],
    template: `Service Provider agrees to provide the following services: [Service Description]. Services shall be performed in a professional manner according to industry standards.`,
    complexity: 'simple',
    isRequired: true,
    riskLevel: 'medium'
  },
  {
    id: 'governing-law',
    title: 'Governing Law',
    description: 'Specifies which jurisdiction\'s laws apply',
    category: 'legal',
    contractTypes: ['nda', 'employment', 'service', 'lease', 'partnership', 'custom'],
    template: `This Agreement shall be governed by and construed in accordance with the laws of [Jurisdiction], without regard to conflict of laws principles.`,
    complexity: 'simple',
    riskLevel: 'low'
  }
];

// Quick presets per contract type (simple, sensible defaults)
const clausePresets: Record<string, { id: string; name: string; templateIds: string[] }[]> = {
  nda: [
    { id: 'nda-quick', name: 'Quick NDA', templateIds: ['nda-definition', 'governing-law'] },
  ],
  employment: [
    { id: 'employment-core', name: 'Core Employment', templateIds: ['employment-duties', 'governing-law'] },
  ],
  service: [
    { id: 'service-starter', name: 'Service Starter', templateIds: ['service-scope', 'governing-law'] },
  ],
  lease: [
    { id: 'lease-basics', name: 'Lease Basics', templateIds: ['governing-law'] },
  ],
  partnership: [
    { id: 'partner-essentials', name: 'Partner Essentials', templateIds: ['governing-law'] },
  ],
  custom: [
    { id: 'custom-minimal', name: 'Minimal Set', templateIds: ['governing-law'] },
  ],
};

// Enhanced AI clause generation
const generateAIClause = async (prompt: string, contractType: string): Promise<string> => {
  await new Promise(resolve => setTimeout(resolve, 1500));
  return `[AI Generated] Professional ${contractType} clause for: ${prompt}. This clause incorporates industry best practices and legal standards for comprehensive protection and clarity.`;
};

const EnhancedAIClauseBuilder: React.FC<AIClauseBuilderProps> = ({
  contractType,
  formData,
  onAddClause,
  onRemoveClause,
  onReorderClauses,
  existingClauses,
  className = '',
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  // Removed card expansion in new minimalist design
  const [conflicts, setConflicts] = useState<string[]>([]);
  const [editingClauseId, setEditingClauseId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState<string>('');
  const [editContent, setEditContent] = useState<string>('');
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const [selectedPresetId, setSelectedPresetId] = useState<string>('');
  // Quick filter chips
  const [complexityFilter, setComplexityFilter] = useState<Set<string>>(new Set());
  const [riskFilter, setRiskFilter] = useState<Set<string>>(new Set());
  // Undo snackbar state
  const [undoPayload, setUndoPayload] = useState<{ids: string[]; label: string} | null>(null);
  const undoTimerRef = React.useRef<number | null>(null);
  // Persist UI state
  const LS_KEY = 'clauseStudioState';
  // Drag & drop
  const [dragIndex, setDragIndex] = useState<number | null>(null);
  const handleDragStart = (index: number) => setDragIndex(index);
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, _index: number) => {
    // Allow drop
    e.preventDefault();
  };
  const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number) => {
    e.preventDefault();
    if (dragIndex === null || dragIndex === dropIndex || !onReorderClauses) { setDragIndex(null); return; }
    const updated = [...existingClauses];
    const [moved] = updated.splice(dragIndex, 1);
    updated.splice(dropIndex, 0, moved);
    onReorderClauses(updated);
    setFocusedIndex(dropIndex);
    setDragIndex(null);
  };
  const handleDragEnd = () => setDragIndex(null);

  // Get relevant templates
  const relevantTemplates = clauseTemplates.filter(template => 
    !contractType || template.contractTypes.includes(contractType.id)
  );

  const categories = ['all', ...Array.from(new Set(relevantTemplates.map(t => t.category)))];

  const filteredTemplates = relevantTemplates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesComplexity = complexityFilter.size === 0 || complexityFilter.has(template.complexity);
    const matchesRisk = riskFilter.size === 0 || (template.riskLevel ? riskFilter.has(template.riskLevel) : false) || (riskFilter.size === 0);
    return matchesCategory && matchesSearch && matchesComplexity && matchesRisk;
  });

  // Detect conflicts
  const detectConflicts = useCallback(() => {
    const conflictingClauses: string[] = [];
    existingClauses.forEach(clause => {
      if (clause.conflictsWith) {
        const hasConflict = existingClauses.some(other => 
          clause.conflictsWith!.includes(other.id)
        );
        if (hasConflict) conflictingClauses.push(clause.title);
      }
    });
    setConflicts(conflictingClauses);
  }, [existingClauses]);

  useEffect(() => {
    detectConflicts();
  }, [detectConflicts]);

  // Load persisted UI preferences
  useEffect(() => {
    try {
      const raw = localStorage.getItem(LS_KEY);
      if (raw) {
        const v = JSON.parse(raw) as {category?: string; search?: string; presetId?: string; complexity?: string[]; risk?: string[]};
        if (v.category) setSelectedCategory(v.category);
        if (v.search) setSearchTerm(v.search);
        if (v.presetId) setSelectedPresetId(v.presetId);
        if (v.complexity) setComplexityFilter(new Set(v.complexity));
        if (v.risk) setRiskFilter(new Set(v.risk));
      }
    } catch {}
  }, []);

  // Persist when state changes
  useEffect(() => {
    try {
      localStorage.setItem(LS_KEY, JSON.stringify({
        category: selectedCategory,
        search: searchTerm,
        presetId: selectedPresetId,
        complexity: Array.from(complexityFilter),
        risk: Array.from(riskFilter),
      }));
    } catch {}
  }, [selectedCategory, searchTerm, selectedPresetId, complexityFilter, riskFilter]);

  const generateFromTemplate = (template: ClauseTemplate) => {
    const clause: Clause = {
      id: `${template.id}-${Date.now()}`,
      title: template.title,
      content: template.template,
      category: template.category,
      complexity: template.complexity,
      isRequired: template.isRequired,
      riskLevel: template.riskLevel
    };
    onAddClause(clause);
    setUndoPayload({ ids: [clause.id], label: `Added "${template.title}"` });
    if (undoTimerRef.current) window.clearTimeout(undoTimerRef.current);
    undoTimerRef.current = window.setTimeout(() => setUndoPayload(null), 3000);
  };



  const handleAIGeneration = async () => {
    if (!customPrompt.trim() || !contractType) return;

    setIsGenerating(true);
    try {
      const generatedContent = await generateAIClause(customPrompt, contractType.id);
      const clause: Clause = {
        id: `ai-${Date.now()}`,
        title: `Custom: ${customPrompt}`,
        content: generatedContent,
        category: 'custom',
        complexity: 'standard',
        isCustom: true,
        aiGenerated: true
      };
      onAddClause(clause);
      setUndoPayload({ ids: [clause.id], label: `Generated "${clause.title}"` });
      if (undoTimerRef.current) window.clearTimeout(undoTimerRef.current);
      undoTimerRef.current = window.setTimeout(() => setUndoPayload(null), 3000);
      setCustomPrompt('');
    } catch (error) {
      console.error('AI generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // no-op: expansion removed

  // Reordering handlers
  const handleMoveClauseUp = (index: number) => {
    if (index === 0 || !onReorderClauses) return;
    
    const newClauses = [...existingClauses];
    [newClauses[index], newClauses[index - 1]] = [newClauses[index - 1], newClauses[index]];
    onReorderClauses(newClauses);
  };

  const handleMoveClauseDown = (index: number) => {
    if (index === existingClauses.length - 1 || !onReorderClauses) return;
    
    const newClauses = [...existingClauses];
    [newClauses[index], newClauses[index + 1]] = [newClauses[index + 1], newClauses[index]];
    onReorderClauses(newClauses);
  };

  const handleRemoveClause = (clauseId: string) => {
    if (onRemoveClause) {
      onRemoveClause(clauseId);
    }
  };

  const startEditing = (clauseId: string, title: string, content: string) => {
    if (!onReorderClauses) return; // editing requires ability to emit updated array
    setEditingClauseId(clauseId);
    setEditTitle(title);
    setEditContent(content);
  };

  const cancelEditing = () => {
    setEditingClauseId(null);
    setEditTitle('');
    setEditContent('');
  };

  const commitEditing = () => {
    if (!editingClauseId || !onReorderClauses) return;
    const newClauses = existingClauses.map(c =>
      c.id === editingClauseId ? { ...c, title: editTitle.trim() || c.title, content: editContent } : c
    );
    onReorderClauses(newClauses);
    cancelEditing();
  };

  const handleClauseKeyDown = (e: React.KeyboardEvent<HTMLDivElement>, index: number, clauseId: string) => {
    if (editingClauseId) return; // don't interfere when editing
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      handleMoveClauseUp(index);
      setFocusedIndex(Math.max(0, index - 1));
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      handleMoveClauseDown(index);
      setFocusedIndex(Math.min(existingClauses.length - 1, index + 1));
    } else if (e.key === 'Backspace' || e.key === 'Delete') {
      e.preventDefault();
      handleRemoveClause(clauseId);
      setFocusedIndex(Math.min(existingClauses.length - 2, index));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      const c = existingClauses[index];
      startEditing(c.id, c.title, c.content);
    }
  };

  const getRiskColor = (risk?: string) => {
    switch (risk) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Preset helpers
  const availablePresets = contractType ? (clausePresets[contractType.id] || []) : [];
  const applyPreset = (presetId: string) => {
    if (!contractType || !presetId) return;
    const preset = availablePresets.find(p => p.id === presetId);
    if (!preset) return;
    const existingTitles = new Set(existingClauses.map(c => c.title));
    const toAdd: Clause[] = [];
    preset.templateIds.forEach(tid => {
      const t = clauseTemplates.find(ct => ct.id === tid);
      if (!t) return;
      if (!existingTitles.has(t.title)) {
        toAdd.push({
          id: `${t.id}-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,
          title: t.title,
          content: t.template,
          category: t.category,
          complexity: t.complexity,
          isRequired: t.isRequired,
          riskLevel: t.riskLevel,
        });
      }
    });
    if (toAdd.length === 0) return;
    if (onReorderClauses) {
      onReorderClauses([...existingClauses, ...toAdd]);
    } else {
      toAdd.forEach(c => onAddClause(c));
    }
    setUndoPayload({ ids: toAdd.map(c=>c.id), label: `Applied preset (${toAdd.length} added)` });
    if (undoTimerRef.current) window.clearTimeout(undoTimerRef.current);
    undoTimerRef.current = window.setTimeout(() => setUndoPayload(null), 3500);
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Enhanced Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-display font-semibold tracking-tight text-gray-900 dark:text-white">
              Clause Studio
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1 text-sm">
              Build your contract with precision and clarity
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 px-3 py-1.5 rounded-full">
              {existingClauses.length} clause{existingClauses.length !== 1 ? 's' : ''} selected
            </div>
          </div>
        </div>
      </div>

      {/* Conflict Alert - Enhanced */}
      {conflicts.length > 0 && (
        <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl">
          <div className="flex items-start gap-3">
            <div className="w-5 h-5 rounded-full bg-amber-100 dark:bg-amber-900/40 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-amber-600 dark:text-amber-400 text-xs">⚠</span>
            </div>
            <div>
              <h4 className="font-medium text-amber-800 dark:text-amber-200 text-sm">
                Potential Conflicts Detected
              </h4>
              <p className="text-amber-700 dark:text-amber-300 text-xs mt-1">
                {conflicts.length} clause{conflicts.length !== 1 ? 's' : ''} may have conflicting terms: {conflicts.join(', ')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Redesigned Studio Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Simplified Category Sidebar */}
        <aside className="lg:col-span-1">
          <div className="sticky top-4">
            <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Categories</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                    selectedCategory === 'all'
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  All Templates
                </button>
                {categories.filter(c => c !== 'all').map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm capitalize transition-colors ${
                      selectedCategory === category
                        ? 'bg-primary-500 text-white shadow-sm'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                  >
                    {category.replace(/([A-Z])/g, ' $1').trim()}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </aside>

        {/* Enhanced Template Library */}
        <section className="lg:col-span-2">
          <div className="space-y-4">
            {/* Enhanced Search Bar */}
            <SearchInput
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="Search clause templates..."
              size="md"
            />

            {/* Simplified Filters */}
            <div className="flex flex-wrap gap-2">
              {(['simple','standard','complex'] as const).map(c => (
                <button
                  key={c}
                  onClick={() => {
                    const next = new Set(complexityFilter);
                    if (next.has(c)) next.delete(c); else next.add(c);
                    setComplexityFilter(next);
                  }}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all ${
                    complexityFilter.has(c)
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                  }`}
                  title={`Filter ${c} templates`}
                  aria-pressed={complexityFilter.has(c)}
                >
                  {c.charAt(0).toUpperCase() + c.slice(1)}
                </button>
              ))}
              {(complexityFilter.size > 0 || riskFilter.size > 0) && (
                <button
                  onClick={() => { setComplexityFilter(new Set()); setRiskFilter(new Set()); }}
                  className="px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all"
                  title="Clear filters"
                >
                  Clear filters
                </button>
              )}
            </div>
          </div>
          {/* Enhanced Template Grid */}
          <div className="mt-6 grid grid-cols-1 gap-3">
            {filteredTemplates.map((template) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                className="group bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-4 hover:border-primary-300 dark:hover:border-primary-600 hover:shadow-sm transition-all cursor-pointer"
                onClick={() => generateFromTemplate(template)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {template.title}
                      </h4>
                      {template.isRequired && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                          Required
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                      {template.description}
                    </p>
                    <div className="flex items-center gap-2">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        template.complexity === 'simple' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                        template.complexity === 'standard' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                      }`}>
                        {template.complexity}
                      </span>
                      {template.riskLevel && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRiskColor(template.riskLevel)}`}>
                          {template.riskLevel} risk
                        </span>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      generateFromTemplate(template);
                    }}
                    className="ml-3 flex-shrink-0 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    title="Add clause"
                    aria-label={`Add ${template.title}`}
                  >
                    Add
                  </button>
                </div>
              </motion.div>
            ))}
            {filteredTemplates.length === 0 && (
              <div className="text-center py-12">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                  <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">No templates found</p>
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Try adjusting your search or filters</p>
              </div>
            )}
          </div>
        </section>

        {/* Enhanced Sidebar */}
        <aside className="lg:col-span-1">
          <div className="space-y-6 sticky top-4">
            {/* AI Composer - Simplified */}
            <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">AI Composer</h3>
              </div>
              <div className="space-y-3">
                <textarea
                  placeholder="Describe the clause you need..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all text-sm resize-none"
                  aria-label="AI prompt"
                />
                <button
                  onClick={handleAIGeneration}
                  disabled={!customPrompt.trim() || isGenerating}
                  className="w-full px-4 py-2 bg-primary-500 hover:bg-primary-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  aria-live="polite"
                  title="Generate clause with AI"
                >
                  {isGenerating ? (
                    <span className="flex items-center justify-center gap-2">
                      <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating...
                    </span>
                  ) : (
                    'Generate Clause'
                  )}
                </button>
              </div>
            </div>

            {/* Quick Presets - Simplified */}
            {availablePresets.length > 0 && (
              <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Quick Start</h3>
                <div className="space-y-2">
                  {availablePresets.map(preset => (
                    <button
                      key={preset.id}
                      onClick={() => applyPreset(preset.id)}
                      className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                    >
                      {preset.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Selected Clauses - Enhanced */}
            <div className="bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Your Contract ({existingClauses.length})
                  </h3>
                  {existingClauses.length > 0 && (
                    <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
                      {existingClauses.length} clause{existingClauses.length !== 1 ? 's' : ''}
                    </span>
                  )}
                </div>
              </div>
              <div className="p-4 max-h-[500px] overflow-y-auto">
                {existingClauses.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 rounded-2xl bg-gray-50 dark:bg-gray-800 mx-auto mb-4 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">No clauses added yet</p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Start by selecting templates or using AI</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {existingClauses.map((clause, index) => (
                      <div
                        key={clause.id}
                        className={`group bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-3 hover:border-primary-300 dark:hover:border-primary-600 transition-all ${focusedIndex===index ? 'ring-2 ring-primary-500' : ''} ${dragIndex===index ? 'opacity-60' : ''}`}
                        tabIndex={0}
                        onFocus={() => setFocusedIndex(index)}
                        onKeyDown={(e) => handleClauseKeyDown(e, index, clause.id)}
                        draggable
                        onDragStart={() => handleDragStart(index)}
                        onDragOver={(e) => handleDragOver(e, index)}
                        onDrop={(e) => handleDrop(e, index)}
                        onDragEnd={handleDragEnd}
                        aria-grabbed={dragIndex===index}
                        aria-dropeffect="move"
                      >
                        <div className="flex items-start gap-3">
                          {/* Drag Handle */}
                          <div className="flex-shrink-0 mt-1">
                            <div className="w-4 h-4 flex items-center justify-center cursor-grab active:cursor-grabbing">
                              <svg className="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                              </svg>
                            </div>
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            {editingClauseId === clause.id ? (
                              <div className="space-y-3">
                                <input
                                  value={editTitle}
                                  onChange={(e) => setEditTitle(e.target.value)}
                                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500"
                                  placeholder="Clause title"
                                />
                                <textarea
                                  value={editContent}
                                  onChange={(e) => setEditContent(e.target.value)}
                                  rows={4}
                                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
                                  placeholder="Clause content"
                                />
                                <div className="flex gap-2">
                                  <button
                                    onClick={commitEditing}
                                    className="px-3 py-1.5 text-xs font-medium rounded-lg bg-primary-500 text-white hover:bg-primary-600 transition-colors"
                                  >
                                    Save
                                  </button>
                                  <button
                                    onClick={cancelEditing}
                                    className="px-3 py-1.5 text-xs font-medium rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="flex items-start justify-between gap-2 mb-2">
                                  <h4 className="text-sm font-medium text-gray-900 dark:text-white line-clamp-1">
                                    {clause.title}
                                  </h4>
                                  <span className="text-xs text-gray-400 dark:text-gray-500 flex-shrink-0">
                                    {index + 1}
                                  </span>
                                </div>
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                                  {clause.content}
                                </p>
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                      clause.complexity === 'simple' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                      clause.complexity === 'standard' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                                      'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                                    }`}>
                                      {clause.complexity}
                                    </span>
                                    {clause.aiGenerated && (
                                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                                        AI
                                      </span>
                                    )}
                                    {clause.isRequired && (
                                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                        Required
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </>
                            )}
                          </div>
                          {/* Action Buttons - Simplified */}
                          {editingClauseId !== clause.id && (
                            <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="flex items-center gap-1">
                                <button
                                  onClick={() => startEditing(clause.id, clause.title, clause.content)}
                                  className="p-1.5 rounded-lg hover:bg-white dark:hover:bg-gray-700 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                  title="Edit clause"
                                  aria-label={`Edit ${clause.title}`}
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </button>
                                <button
                                  onClick={() => handleMoveClauseUp(index)}
                                  disabled={index === 0}
                                  className="p-1.5 rounded-lg hover:bg-white dark:hover:bg-gray-700 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
                                  title="Move up"
                                  aria-label={`Move ${clause.title} up`}
                                >
                                  <ChevronUp className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleMoveClauseDown(index)}
                                  disabled={index === existingClauses.length - 1}
                                  className="p-1.5 rounded-lg hover:bg-white dark:hover:bg-gray-700 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
                                  title="Move down"
                                  aria-label={`Move ${clause.title} down`}
                                >
                                  <ChevronDown className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleRemoveClause(clause.id)}
                                  className="p-1.5 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 text-gray-400 hover:text-red-500 transition-colors"
                                  title="Remove clause"
                                  aria-label={`Remove ${clause.title}`}
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </aside>
      </div>
      {/* Enhanced Undo Snackbar */}
      {undoPayload && (
        <div className="fixed bottom-6 right-6 z-50">
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            className="flex items-center gap-3 px-4 py-3 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm backdrop-blur-sm"
          >
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <svg className="w-3 h-3 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-gray-800 dark:text-gray-100 font-medium">{undoPayload.label}</span>
            </div>
            <button
              className="px-3 py-1.5 text-xs font-medium rounded-lg bg-primary-500 text-white hover:bg-primary-600 transition-colors"
              onClick={() => {
                if (!onReorderClauses || undoPayload.ids.length === 0) {
                  setUndoPayload(null);
                  return;
                }
                const remaining = existingClauses.filter(c => !undoPayload.ids.includes(c.id));
                onReorderClauses(remaining);
                setUndoPayload(null);
              }}
              title="Undo action"
            >
              Undo
            </button>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAIClauseBuilder;
