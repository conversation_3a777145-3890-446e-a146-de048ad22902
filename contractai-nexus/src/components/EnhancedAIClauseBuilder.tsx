import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { ChevronUp, ChevronDown, X } from 'lucide-react';
import { ContractType } from './ContractGalaxy';
import { Clause } from '../types/clauses';

// Clause type now comes from shared src/types/clauses

interface ClauseTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  contractTypes: string[];
  template: string;
  complexity: 'simple' | 'standard' | 'complex';
  isRequired?: boolean;
  riskLevel?: 'low' | 'medium' | 'high';
}

interface AIClauseBuilderProps {
  contractType: ContractType | null;
  formData: Record<string, any>;
  onAddClause: (clause: Clause) => void;
  onRemoveClause?: (clauseId: string) => void;
  onReorderClauses?: (reorderedClauses: Clause[]) => void;
  existingClauses: Clause[];
  className?: string;
}

// Enhanced clause templates
const clauseTemplates: ClauseTemplate[] = [
  {
    id: 'nda-definition',
    title: 'Confidential Information Definition',
    description: 'Defines what constitutes confidential information',
    category: 'definitions',
    contractTypes: ['nda'],
    template: `"Confidential Information" means any non-public information disclosed by one party to the other, including technical data, trade secrets, business information, and proprietary materials.`,
    complexity: 'standard',
    isRequired: true,
    riskLevel: 'medium'
  },
  {
    id: 'employment-duties',
    title: 'Job Duties and Responsibilities',
    description: 'Defines the employee\'s role and responsibilities',
    category: 'duties',
    contractTypes: ['employment'],
    template: `Employee shall serve in the position of [Job Title] and perform duties customarily associated with such position, devoting full business time and attention to the performance of duties.`,
    complexity: 'simple',
    isRequired: true,
    riskLevel: 'low'
  },
  {
    id: 'service-scope',
    title: 'Scope of Services',
    description: 'Defines the services to be provided',
    category: 'scope',
    contractTypes: ['service'],
    template: `Service Provider agrees to provide the following services: [Service Description]. Services shall be performed in a professional manner according to industry standards.`,
    complexity: 'simple',
    isRequired: true,
    riskLevel: 'medium'
  },
  {
    id: 'governing-law',
    title: 'Governing Law',
    description: 'Specifies which jurisdiction\'s laws apply',
    category: 'legal',
    contractTypes: ['nda', 'employment', 'service', 'lease', 'partnership', 'custom'],
    template: `This Agreement shall be governed by and construed in accordance with the laws of [Jurisdiction], without regard to conflict of laws principles.`,
    complexity: 'simple',
    riskLevel: 'low'
  }
];

// Quick presets per contract type (simple, sensible defaults)
const clausePresets: Record<string, { id: string; name: string; templateIds: string[] }[]> = {
  nda: [
    { id: 'nda-quick', name: 'Quick NDA', templateIds: ['nda-definition', 'governing-law'] },
  ],
  employment: [
    { id: 'employment-core', name: 'Core Employment', templateIds: ['employment-duties', 'governing-law'] },
  ],
  service: [
    { id: 'service-starter', name: 'Service Starter', templateIds: ['service-scope', 'governing-law'] },
  ],
  lease: [
    { id: 'lease-basics', name: 'Lease Basics', templateIds: ['governing-law'] },
  ],
  partnership: [
    { id: 'partner-essentials', name: 'Partner Essentials', templateIds: ['governing-law'] },
  ],
  custom: [
    { id: 'custom-minimal', name: 'Minimal Set', templateIds: ['governing-law'] },
  ],
};

// Enhanced AI clause generation
const generateAIClause = async (prompt: string, contractType: string): Promise<string> => {
  await new Promise(resolve => setTimeout(resolve, 1500));
  return `[AI Generated] Professional ${contractType} clause for: ${prompt}. This clause incorporates industry best practices and legal standards for comprehensive protection and clarity.`;
};

const EnhancedAIClauseBuilder: React.FC<AIClauseBuilderProps> = ({
  contractType,
  formData,
  onAddClause,
  onRemoveClause,
  onReorderClauses,
  existingClauses,
  className = '',
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  // Removed card expansion in new minimalist design
  const [conflicts, setConflicts] = useState<string[]>([]);
  const [editingClauseId, setEditingClauseId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState<string>('');
  const [editContent, setEditContent] = useState<string>('');
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const [selectedPresetId, setSelectedPresetId] = useState<string>('');
  // Quick filter chips
  const [complexityFilter, setComplexityFilter] = useState<Set<string>>(new Set());
  const [riskFilter, setRiskFilter] = useState<Set<string>>(new Set());
  // Undo snackbar state
  const [undoPayload, setUndoPayload] = useState<{ids: string[]; label: string} | null>(null);
  const undoTimerRef = React.useRef<number | null>(null);
  // Persist UI state
  const LS_KEY = 'clauseStudioState';
  // Drag & drop
  const [dragIndex, setDragIndex] = useState<number | null>(null);
  const handleDragStart = (index: number) => setDragIndex(index);
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, _index: number) => {
    // Allow drop
    e.preventDefault();
  };
  const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number) => {
    e.preventDefault();
    if (dragIndex === null || dragIndex === dropIndex || !onReorderClauses) { setDragIndex(null); return; }
    const updated = [...existingClauses];
    const [moved] = updated.splice(dragIndex, 1);
    updated.splice(dropIndex, 0, moved);
    onReorderClauses(updated);
    setFocusedIndex(dropIndex);
    setDragIndex(null);
  };
  const handleDragEnd = () => setDragIndex(null);

  // Get relevant templates
  const relevantTemplates = clauseTemplates.filter(template => 
    !contractType || template.contractTypes.includes(contractType.id)
  );

  const categories = ['all', ...Array.from(new Set(relevantTemplates.map(t => t.category)))];

  const filteredTemplates = relevantTemplates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesComplexity = complexityFilter.size === 0 || complexityFilter.has(template.complexity);
    const matchesRisk = riskFilter.size === 0 || (template.riskLevel ? riskFilter.has(template.riskLevel) : false) || (riskFilter.size === 0);
    return matchesCategory && matchesSearch && matchesComplexity && matchesRisk;
  });

  // Detect conflicts
  const detectConflicts = useCallback(() => {
    const conflictingClauses: string[] = [];
    existingClauses.forEach(clause => {
      if (clause.conflictsWith) {
        const hasConflict = existingClauses.some(other => 
          clause.conflictsWith!.includes(other.id)
        );
        if (hasConflict) conflictingClauses.push(clause.title);
      }
    });
    setConflicts(conflictingClauses);
  }, [existingClauses]);

  useEffect(() => {
    detectConflicts();
  }, [detectConflicts]);

  // Load persisted UI preferences
  useEffect(() => {
    try {
      const raw = localStorage.getItem(LS_KEY);
      if (raw) {
        const v = JSON.parse(raw) as {category?: string; search?: string; presetId?: string; complexity?: string[]; risk?: string[]};
        if (v.category) setSelectedCategory(v.category);
        if (v.search) setSearchTerm(v.search);
        if (v.presetId) setSelectedPresetId(v.presetId);
        if (v.complexity) setComplexityFilter(new Set(v.complexity));
        if (v.risk) setRiskFilter(new Set(v.risk));
      }
    } catch {}
  }, []);

  // Persist when state changes
  useEffect(() => {
    try {
      localStorage.setItem(LS_KEY, JSON.stringify({
        category: selectedCategory,
        search: searchTerm,
        presetId: selectedPresetId,
        complexity: Array.from(complexityFilter),
        risk: Array.from(riskFilter),
      }));
    } catch {}
  }, [selectedCategory, searchTerm, selectedPresetId, complexityFilter, riskFilter]);

  const generateFromTemplate = (template: ClauseTemplate) => {
    const clause: Clause = {
      id: `${template.id}-${Date.now()}`,
      title: template.title,
      content: template.template,
      category: template.category,
      complexity: template.complexity,
      isRequired: template.isRequired,
      riskLevel: template.riskLevel
    };
    onAddClause(clause);
    setUndoPayload({ ids: [clause.id], label: `Added "${template.title}"` });
    if (undoTimerRef.current) window.clearTimeout(undoTimerRef.current);
    undoTimerRef.current = window.setTimeout(() => setUndoPayload(null), 3000);
  };

  // Helpers for deeplinking to preview anchors
  const slugify = (text: string) =>
    (text || '')
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .trim()
      .replace(/\s+/g, '-');

  const viewInPreview = (index: number, title: string) => {
    const anchorId = `clause-${index + 1}-${slugify(title)}`;
    const el = document.getElementById(anchorId);
    if (el && 'scrollIntoView' in el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    if (typeof window !== 'undefined') {
      window.location.hash = anchorId;
    }
  };

  const handleAIGeneration = async () => {
    if (!customPrompt.trim() || !contractType) return;

    setIsGenerating(true);
    try {
      const generatedContent = await generateAIClause(customPrompt, contractType.id);
      const clause: Clause = {
        id: `ai-${Date.now()}`,
        title: `Custom: ${customPrompt}`,
        content: generatedContent,
        category: 'custom',
        complexity: 'standard',
        isCustom: true,
        aiGenerated: true
      };
      onAddClause(clause);
      setUndoPayload({ ids: [clause.id], label: `Generated "${clause.title}"` });
      if (undoTimerRef.current) window.clearTimeout(undoTimerRef.current);
      undoTimerRef.current = window.setTimeout(() => setUndoPayload(null), 3000);
      setCustomPrompt('');
    } catch (error) {
      console.error('AI generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // no-op: expansion removed

  // Reordering handlers
  const handleMoveClauseUp = (index: number) => {
    if (index === 0 || !onReorderClauses) return;
    
    const newClauses = [...existingClauses];
    [newClauses[index], newClauses[index - 1]] = [newClauses[index - 1], newClauses[index]];
    onReorderClauses(newClauses);
  };

  const handleMoveClauseDown = (index: number) => {
    if (index === existingClauses.length - 1 || !onReorderClauses) return;
    
    const newClauses = [...existingClauses];
    [newClauses[index], newClauses[index + 1]] = [newClauses[index + 1], newClauses[index]];
    onReorderClauses(newClauses);
  };

  const handleRemoveClause = (clauseId: string) => {
    if (onRemoveClause) {
      onRemoveClause(clauseId);
    }
  };

  const startEditing = (clauseId: string, title: string, content: string) => {
    if (!onReorderClauses) return; // editing requires ability to emit updated array
    setEditingClauseId(clauseId);
    setEditTitle(title);
    setEditContent(content);
  };

  const cancelEditing = () => {
    setEditingClauseId(null);
    setEditTitle('');
    setEditContent('');
  };

  const commitEditing = () => {
    if (!editingClauseId || !onReorderClauses) return;
    const newClauses = existingClauses.map(c =>
      c.id === editingClauseId ? { ...c, title: editTitle.trim() || c.title, content: editContent } : c
    );
    onReorderClauses(newClauses);
    cancelEditing();
  };

  const handleClauseKeyDown = (e: React.KeyboardEvent<HTMLDivElement>, index: number, clauseId: string) => {
    if (editingClauseId) return; // don't interfere when editing
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      handleMoveClauseUp(index);
      setFocusedIndex(Math.max(0, index - 1));
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      handleMoveClauseDown(index);
      setFocusedIndex(Math.min(existingClauses.length - 1, index + 1));
    } else if (e.key === 'Backspace' || e.key === 'Delete') {
      e.preventDefault();
      handleRemoveClause(clauseId);
      setFocusedIndex(Math.min(existingClauses.length - 2, index));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      const c = existingClauses[index];
      startEditing(c.id, c.title, c.content);
    }
  };

  const getRiskColor = (risk?: string) => {
    switch (risk) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Preset helpers
  const availablePresets = contractType ? (clausePresets[contractType.id] || []) : [];
  const applyPreset = (presetId: string) => {
    if (!contractType || !presetId) return;
    const preset = availablePresets.find(p => p.id === presetId);
    if (!preset) return;
    const existingTitles = new Set(existingClauses.map(c => c.title));
    const toAdd: Clause[] = [];
    preset.templateIds.forEach(tid => {
      const t = clauseTemplates.find(ct => ct.id === tid);
      if (!t) return;
      if (!existingTitles.has(t.title)) {
        toAdd.push({
          id: `${t.id}-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,
          title: t.title,
          content: t.template,
          category: t.category,
          complexity: t.complexity,
          isRequired: t.isRequired,
          riskLevel: t.riskLevel,
        });
      }
    });
    if (toAdd.length === 0) return;
    if (onReorderClauses) {
      onReorderClauses([...existingClauses, ...toAdd]);
    } else {
      toAdd.forEach(c => onAddClause(c));
    }
    setUndoPayload({ ids: toAdd.map(c=>c.id), label: `Applied preset (${toAdd.length} added)` });
    if (undoTimerRef.current) window.clearTimeout(undoTimerRef.current);
    undoTimerRef.current = window.setTimeout(() => setUndoPayload(null), 3500);
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">Clause Studio</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">A calm workspace to pick essentials, compose custom terms, and arrange your contract clearly.</p>
      </div>

      {conflicts.length > 0 && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-600">⚠️</span>
            <span className="font-medium text-yellow-800">
              Potential conflicts detected in {conflicts.length} clause(s): {conflicts.join(', ')}
            </span>
          </div>
        </div>
      )}

      {/* New Studio Layout */}
      <div className="grid grid-cols-12 gap-6">
        {/* Category Rail */}
        <aside className="col-span-12 sm:col-span-2 lg:col-span-2">
          <div className="sticky top-4 space-y-2">
            <div className="rounded-xl border border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-800">
              <button
                onClick={() => setSelectedCategory('all')}
                className={`w-full text-left px-3 py-2 rounded-lg text-sm ${selectedCategory==='all' ? 'bg-gray-900 text-white dark:bg-white dark:text-gray-900' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'}`}
              >All</button>
              {categories.filter(c=>c!=='all').map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm capitalize ${selectedCategory===category ? 'bg-gray-900 text-white dark:bg-white dark:text-gray-900' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                >{category}</button>
              ))}
            </div>
          </div>
        </aside>

        {/* Templates Canvas */}
        <section className="col-span-12 sm:col-span-6 lg:col-span-7">
          <div className="mb-3">
            <input
              type="text"
              placeholder="Search templates"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm"
            />
          </div>
          {/* Quick Filters */}
          <div className="mb-4 flex flex-wrap items-center gap-2">
            <span className="text-xs text-gray-500">Complexity:</span>
            {(['simple','standard','complex'] as const).map(c => (
              <button
                key={c}
                onClick={() => {
                  const next = new Set(complexityFilter);
                  if (next.has(c)) next.delete(c); else next.add(c);
                  setComplexityFilter(next);
                }}
                className={`px-2 py-1 rounded-full text-[11px] border ${complexityFilter.has(c) ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300'}`}
                title={`Filter ${c} templates`}
                aria-pressed={complexityFilter.has(c)}
              >{c}</button>
            ))}
            <span className="ml-3 text-xs text-gray-500">Risk:</span>
            {(['low','medium','high'] as const).map(r => (
              <button
                key={r}
                onClick={() => {
                  const next = new Set(riskFilter);
                  if (next.has(r)) next.delete(r); else next.add(r);
                  setRiskFilter(next);
                }}
                className={`px-2 py-1 rounded-full text-[11px] border ${riskFilter.has(r) ? 'bg-gray-900 text-white dark:bg-white dark:text-gray-900 border-gray-900 dark:border-white' : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300'}`}
                title={`Filter ${r} risk`}
                aria-pressed={riskFilter.has(r)}
              >{r}</button>
            ))}
            {(complexityFilter.size>0 || riskFilter.size>0) && (
              <button
                onClick={() => { setComplexityFilter(new Set()); setRiskFilter(new Set()); }}
                className="ml-2 text-xs px-2 py-1 rounded border border-gray-300 dark:border-gray-600"
                title="Clear filters"
              >Clear</button>
            )}
          </div>
          <div className="flex flex-wrap gap-3">
            {filteredTemplates.map((template) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 6 }}
                animate={{ opacity: 1, y: 0 }}
                className="group px-3 py-2 rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 cursor-default"
                >
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">{template.title}</span>
                  {template.isRequired && (
                    <span className="text-[10px] px-2 py-0.5 rounded-full bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300">required</span>
                  )}
                  <span className="text-[10px] px-2 py-0.5 rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">{template.complexity}</span>
                  {template.riskLevel && (
                    <span className={`text-[10px] px-2 py-0.5 rounded-full ${getRiskColor(template.riskLevel)}`}>{template.riskLevel}</span>
                  )}
                  <button
                    onClick={() => generateFromTemplate(template)}
                    className="ml-2 text-xs px-2 py-1 rounded-md bg-gray-900 text-white dark:bg-white dark:text-gray-900 hover:opacity-90"
                    title="Add clause"
                    aria-label={`Add ${template.title}`}
                  >Add</button>
                </div>
                <details className="mt-1">
                  <summary className="text-xs text-gray-500 dark:text-gray-400 cursor-pointer">Preview</summary>
                  <div className="mt-2 text-[11px] leading-relaxed text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/40 border border-gray-100 dark:border-gray-600 rounded-md p-3">
                    {template.template}
                  </div>
                </details>
              </motion.div>
            ))}
            {filteredTemplates.length === 0 && (
              <p className="text-sm text-gray-500 dark:text-gray-400">No templates found.</p>
            )}
          </div>
        </section>

        {/* Composer + Selection */}
        <aside className="col-span-12 sm:col-span-4 lg:col-span-3">
          <div className="space-y-4 sticky top-4">
            {/* Presets */}
            {availablePresets.length > 0 && (
              <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-3">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">Quick Presets</h3>
                  <span className="text-base">⚡</span>
                </div>
                <div className="flex gap-2">
                  <select
                    value={selectedPresetId}
                    onChange={(e) => setSelectedPresetId(e.target.value)}
                    className="flex-1 px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm"
                    title="Select a preset"
                  >
                    <option value="">Select a preset</option>
                    {availablePresets.map(p => (
                      <option key={p.id} value={p.id}>{p.name}</option>
                    ))}
                  </select>
                  <button
                    onClick={() => applyPreset(selectedPresetId)}
                    disabled={!selectedPresetId}
                    className="px-3 py-2 rounded-lg bg-gray-900 text-white dark:bg-white dark:text-gray-900 text-sm disabled:opacity-50 whitespace-nowrap"
                    title="Apply preset"
                  >Apply</button>
                <div className="mt-2 text-[11px] text-gray-500">
                  {selectedPresetId && (() => {
                    const p = availablePresets.find(x=>x.id===selectedPresetId);
                    if (!p) return null;
                    const items = p.templateIds.map(tid => clauseTemplates.find(ct=>ct.id===tid)?.title).filter(Boolean) as string[];
                    return <div className="rounded bg-gray-50 dark:bg-gray-700/40 border border-gray-200 dark:border-gray-700 p-2">{items.length>0 ? `Will add: ${items.join(', ')}` : 'No items in preset'}</div>;
                  })()}
                </div>
                </div>
              </div>
            )}

            {/* AI Composer */}
            <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-3">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">Compose with AI</h3>
                <span className="text-base">🤖</span>
              </div>
              <input
                type="text"
                placeholder="Describe the clause..."
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                onKeyDown={(e) => { if (e.key === 'Enter') handleAIGeneration(); }}
                className="w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm"
                aria-label="AI prompt"
              />
              <button
                onClick={handleAIGeneration}
                disabled={!customPrompt.trim() || isGenerating}
                className="mt-2 w-full px-3 py-2 rounded-lg bg-gray-900 text-white dark:bg-white dark:text-gray-900 text-sm disabled:opacity-50"
                aria-live="polite"
                title="Generate clause with AI"
              >{isGenerating ? 'Generating…' : 'Generate'}</button>
            </div>

            {/* Selected List */}
            <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">Selected ({existingClauses.length})</h3>
              </div>
              <div className="p-3 max-h-[520px] overflow-y-auto">
                {existingClauses.length === 0 ? (
                  <div className="text-center py-10">
                    <div className="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 mx-auto mb-3 flex items-center justify-center">📄</div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">No clauses yet</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {existingClauses.map((clause, index) => (
                      <div
                        key={clause.id}
                        className={`group border border-gray-200 dark:border-gray-600 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 ${focusedIndex===index ? 'ring-2 ring-blue-500' : ''} ${dragIndex===index ? 'opacity-80' : ''}`}
                        tabIndex={0}
                        onFocus={() => setFocusedIndex(index)}
                        onKeyDown={(e) => handleClauseKeyDown(e, index, clause.id)}
                        draggable
                        onDragStart={() => handleDragStart(index)}
                        onDragOver={(e) => handleDragOver(e, index)}
                        onDrop={(e) => handleDrop(e, index)}
                        onDragEnd={handleDragEnd}
                        aria-grabbed={dragIndex===index}
                        aria-dropeffect="move"
                      >
                        <div className="flex items-start justify-between gap-2">
                          <div className="min-w-0">
                            {editingClauseId === clause.id ? (
                              <div className="space-y-2">
                                <input
                                  value={editTitle}
                                  onChange={(e) => setEditTitle(e.target.value)}
                                  className="w-full px-2 py-1 text-sm border rounded-md bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600"
                                  placeholder="Clause title"
                                />
                                <textarea
                                  value={editContent}
                                  onChange={(e) => setEditContent(e.target.value)}
                                  rows={3}
                                  className="w-full px-2 py-1 text-xs border rounded-md bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600"
                                  placeholder="Clause content"
                                />
                                <div className="flex gap-2">
                                  <button onClick={commitEditing} className="px-2 py-1 text-xs rounded bg-blue-600 text-white">Save</button>
                                  <button onClick={cancelEditing} className="px-2 py-1 text-xs rounded border border-gray-300 dark:border-gray-600">Cancel</button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{clause.title}</div>
                                <div className="mt-1 text-[11px] text-gray-600 dark:text-gray-400 line-clamp-2">{clause.content}</div>
                              </>
                            )}
                            <div className="mt-2 flex flex-wrap gap-2">
                              <span className="px-2 py-0.5 text-[10px] rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">{clause.complexity}</span>
                              {clause.aiGenerated && (
                                <span className="px-2 py-0.5 text-[10px] rounded-full bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">AI</span>
                              )}
                              {clause.isRequired && (
                                <span className="px-2 py-0.5 text-[10px] rounded-full bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300">Required</span>
                              )}
                              <span className="text-[10px] text-gray-400">{index + 1} / {existingClauses.length}</span>
                            </div>
                          </div>
                          <div className="flex-shrink-0 flex items-center gap-1">
                            <span className="cursor-grab select-none px-1 text-gray-400" title="Drag to reorder" aria-hidden>⋮⋮</span>
                            <button
                              onClick={() => viewInPreview(index, clause.title)}
                              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600"
                              title="View in preview"
                              aria-label={`View ${clause.title} in preview`}
                            >
                              🔗
                            </button>
                            {editingClauseId !== clause.id && onReorderClauses && (
                              <button
                                onClick={() => startEditing(clause.id, clause.title, clause.content)}
                                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600"
                                title="Edit"
                                aria-label={`Edit ${clause.title}`}
                              >
                                ✏️
                              </button>
                            )}
                            <button
                              onClick={() => handleMoveClauseUp(index)}
                              disabled={index===0}
                              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 disabled:opacity-30"
                              title="Up"
                              aria-label={`Move ${clause.title} up`}
                            >
                              <ChevronUp className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleMoveClauseDown(index)}
                              disabled={index===existingClauses.length-1}
                              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 disabled:opacity-30"
                              title="Down"
                              aria-label={`Move ${clause.title} down`}
                            >
                              <ChevronDown className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleRemoveClause(clause.id)}
                              className="p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/30 text-red-500"
                              title="Remove"
                              aria-label={`Remove ${clause.title}`}
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </aside>
      </div>
      {/* Undo Snackbar */}
      {undoPayload && (
        <div className="fixed bottom-4 right-4 z-40">
          <div className="flex items-center gap-3 px-4 py-3 rounded-lg shadow border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm">
            <span className="text-gray-800 dark:text-gray-100">{undoPayload.label}</span>
            <button
              className="px-2 py-1 text-xs rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
              onClick={() => {
                if (!onReorderClauses || undoPayload.ids.length===0) { setUndoPayload(null); return; }
                const remaining = existingClauses.filter(c => !undoPayload.ids.includes(c.id));
                onReorderClauses(remaining);
                setUndoPayload(null);
              }}
              title="Undo"
            >Undo</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAIClauseBuilder;
