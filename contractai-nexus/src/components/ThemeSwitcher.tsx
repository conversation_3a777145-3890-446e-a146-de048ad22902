import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme, ThemeMode, ThemeAccent } from '../contexts/ThemeContext';

interface ThemeSwitcherProps {
  className?: string;
}

const ThemeSwitcher: React.FC<ThemeSwitcherProps> = ({ className = '' }) => {
  const { mode, accent, setMode, setAccent } = useTheme();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  
  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };
  
  const handleModeChange = (newMode: ThemeMode) => {
    setMode(newMode);
  };
  
  const handleAccentChange = (newAccent: ThemeAccent) => {
    setAccent(newAccent);
  };
  
  // Theme mode options
  const modeOptions: { value: ThemeMode; label: string; icon: React.ReactNode }[] = [
    {
      value: 'light',
      label: 'Light',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      ),
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      ),
    },
    {
      value: 'system',
      label: 'System',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
    },
  ];
  
  // Accent color options
  const accentOptions: { value: ThemeAccent; label: string; colorClass: string }[] = [
    { value: 'blue', label: 'Blue', colorClass: 'bg-blue-500' },
    { value: 'purple', label: 'Purple', colorClass: 'bg-purple-500' },
    { value: 'teal', label: 'Teal', colorClass: 'bg-teal-500' },
    { value: 'amber', label: 'Amber', colorClass: 'bg-amber-500' },
    { value: 'rose', label: 'Rose', colorClass: 'bg-rose-500' },
  ];
  
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={toggleOpen}
        className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
        aria-label="Theme settings"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
        </svg>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.15 }}
            className="absolute right-0 mt-2 w-64 p-4 bg-white dark:bg-dark-200 rounded-lg shadow-lg z-50"
          >
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Theme Mode
              </h3>
              <div className="grid grid-cols-3 gap-2">
                {modeOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleModeChange(option.value)}
                    className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors ${
                      mode === option.value
                        ? 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {option.icon}
                    <span className="text-xs mt-1">{option.label}</span>
                  </button>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Accent Color
              </h3>
              <div className="flex flex-wrap gap-2">
                {accentOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleAccentChange(option.value)}
                    className={`w-8 h-8 rounded-full ${option.colorClass} ${
                      accent === option.value ? 'ring-2 ring-offset-2 ring-gray-400 dark:ring-gray-600' : ''
                    }`}
                    aria-label={`${option.label} theme`}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ThemeSwitcher;
