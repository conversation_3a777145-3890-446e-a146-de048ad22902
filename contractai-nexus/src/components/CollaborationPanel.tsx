import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CollaborationService, { CollaborationUser, CollaborationUpdate } from '../services/CollaborationService';

interface CollaborationPanelProps {
  contractId: string;
  isOpen: boolean;
  onClose: () => void;
}

const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  contractId,
  isOpen,
  onClose,
}) => {
  const [collaborators, setCollaborators] = useState<CollaborationUser[]>([]);
  const [currentUser, setCurrentUser] = useState<CollaborationUser | null>(null);
  const [userName, setUserName] = useState<string>('');
  const [isEditingName, setIsEditingName] = useState<boolean>(false);
  const [recentActivity, setRecentActivity] = useState<CollaborationUpdate[]>([]);
  const [inviteLink, setInviteLink] = useState<string>('');
  const [isCopied, setIsCopied] = useState<boolean>(false);
  
  const collaborationService = CollaborationService.getInstance();
  
  // Initialize collaboration session
  useEffect(() => {
    if (!contractId) return;
    
    // Get the current user
    const user = collaborationService.getCurrentUser();
    setCurrentUser(user);
    
    if (user) {
      setUserName(user.name);
    }
    
    // Generate a mock invite link
    setInviteLink(`https://contractai-nexus.app/collaborate/${contractId}`);
    
    // Join the session
    const session = collaborationService.joinSession(contractId, null, {}, []);
    setCollaborators(session.users);
    
    // Subscribe to collaboration updates
    const unsubscribe = collaborationService.subscribeToUpdates(contractId, (update) => {
      // Update collaborators list when users join or leave
      if (update.type === 'user_joined' || update.type === 'user_left') {
        const session = collaborationService.getSession(contractId);
        if (session) {
          setCollaborators(session.users);
        }
      }
      
      // Add update to recent activity
      setRecentActivity(prev => {
        const newActivity = [update, ...prev];
        // Limit to 10 most recent activities
        return newActivity.slice(0, 10);
      });
    });
    
    // Simulate other users for demo purposes
    collaborationService.simulateCollaboration(contractId);
    
    // Cleanup when component unmounts
    return () => {
      unsubscribe();
      collaborationService.leaveSession(contractId);
    };
  }, [contractId, collaborationService]);
  
  // Handle user name change
  const handleNameChange = () => {
    if (userName.trim() && currentUser) {
      collaborationService.setCurrentUserName(userName);
      setIsEditingName(false);
    }
  };
  
  // Copy invite link to clipboard
  const copyInviteLink = () => {
    navigator.clipboard.writeText(inviteLink).then(() => {
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    });
  };
  
  // Format activity message
  const formatActivityMessage = (update: CollaborationUpdate): string => {
    const user = collaborators.find(u => u.id === update.userId);
    const userName = user?.name || 'Unknown user';
    
    switch (update.type) {
      case 'user_joined':
        return `${userName} joined the collaboration`;
      case 'user_left':
        return `${userName} left the collaboration`;
      case 'form':
        return `${userName} updated contract details`;
      case 'clause':
        return `${userName} modified contract clauses`;
      default:
        return `${userName} made changes`;
    }
  };
  
  // Format timestamp
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ duration: 0.3 }}
          className="fixed top-0 right-0 h-full w-80 bg-white dark:bg-dark-200 shadow-xl z-50 overflow-y-auto"
        >
          <div className="p-4">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Collaboration</h2>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                aria-label="Close collaboration panel"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* Current user */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">You</h3>
              <div className="flex items-center">
                <div 
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white"
                  style={{ backgroundColor: currentUser?.color || '#333' }}
                >
                  {currentUser?.avatar || '👤'}
                </div>
                {isEditingName ? (
                  <div className="ml-2 flex-1">
                    <input
                      type="text"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-700 rounded"
                      onBlur={handleNameChange}
                      onKeyPress={(e) => e.key === 'Enter' && handleNameChange()}
                      autoFocus
                    />
                  </div>
                ) : (
                  <div className="ml-2 flex-1 flex items-center">
                    <span className="text-gray-900 dark:text-white">{currentUser?.name}</span>
                    <button
                      onClick={() => setIsEditingName(true)}
                      className="ml-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      aria-label="Edit name"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            {/* Invite others */}
            <div className="mb-6 p-3 bg-gray-50 dark:bg-dark-300 rounded-lg">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Invite collaborators</h3>
              <div className="flex">
                <input
                  type="text"
                  value={inviteLink}
                  readOnly
                  className="flex-1 px-2 py-1 text-xs border border-gray-300 dark:border-gray-700 rounded-l-md bg-white dark:bg-dark-200"
                />
                <button
                  onClick={copyInviteLink}
                  className={`px-2 py-1 text-xs rounded-r-md ${
                    isCopied 
                      ? 'bg-green-500 text-white' 
                      : 'bg-primary-600 hover:bg-primary-700 text-white'
                  }`}
                >
                  {isCopied ? 'Copied!' : 'Copy'}
                </button>
              </div>
            </div>
            
            {/* Active collaborators */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                Active collaborators ({collaborators.filter(c => c.isActive && c.id !== currentUser?.id).length})
              </h3>
              {collaborators.filter(c => c.isActive && c.id !== currentUser?.id).length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">No other active collaborators</p>
              ) : (
                <div className="space-y-2">
                  {collaborators
                    .filter(c => c.isActive && c.id !== currentUser?.id)
                    .map(collaborator => (
                      <div key={collaborator.id} className="flex items-center">
                        <div 
                          className="w-8 h-8 rounded-full flex items-center justify-center text-white"
                          style={{ backgroundColor: collaborator.color }}
                        >
                          {collaborator.avatar || '👤'}
                        </div>
                        <span className="ml-2 text-gray-900 dark:text-white">{collaborator.name}</span>
                      </div>
                    ))}
                </div>
              )}
            </div>
            
            {/* Recent activity */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Recent activity</h3>
              {recentActivity.length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
              ) : (
                <div className="space-y-2">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-900 dark:text-white">{formatActivityMessage(activity)}</span>
                        <span className="text-gray-500 dark:text-gray-400 text-xs">{formatTimestamp(activity.timestamp)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CollaborationPanel;
