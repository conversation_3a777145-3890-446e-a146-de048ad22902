import React, { useState } from 'react';
import ContractGalaxy, { ContractType } from './ContractGalaxy';
import EnhancedSmartForm from './EnhancedSmartForm';
import EnhancedAIClauseBuilder from './EnhancedAIClauseBuilder';
import { Clause } from '../types/clauses';
import ContractPreview from './ContractPreview';
import SimpleProgressBar, { SimpleStage } from './SimpleProgressBar';
import SmartTemplates from './SmartTemplates';
import { useExpressMode } from '../contexts/ExpressModeContext';
import { ContractTemplate } from '../data/contractTemplates';
import { getAutoFillDefaults } from '../utils/autoFillDefaults';

// Minimal redesign goals:
// - Clean, calm canvas with generous whitespace
// - Thin typography hierarchy, no heavy shadows or animations
// - One primary action per view
// - Subtle step indicator using existing SimpleProgressBar

export enum CreationStage {
  SELECT_TYPE = 0,
  FILL_FORM = 1,
  BUILD_CLAUSES = 2,
  PREVIEW = 3,
}

const MinimalContractGenerator: React.FC = () => {
  const { isExpressMode } = useExpressMode();

  const [stage, setStage] = useState<CreationStage>(CreationStage.SELECT_TYPE);
  const [selectedContract, setSelectedContract] = useState<ContractType | null>(null);
  // Minimal state: avoid storing template unless needed
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [clauses, setClauses] = useState<Clause[]>([]);
  const [showTemplates, setShowTemplates] = useState<boolean>(true);

  const getSimpleStage = (s: CreationStage): SimpleStage => {
    switch (s) {
      case CreationStage.SELECT_TYPE: return SimpleStage.SELECT_TYPE;
      case CreationStage.FILL_FORM: return SimpleStage.FILL_FORM;
      case CreationStage.BUILD_CLAUSES: return SimpleStage.BUILD_CLAUSES;
      case CreationStage.PREVIEW: return SimpleStage.PREVIEW_SIGN;
      default: return SimpleStage.SELECT_TYPE;
    }
  };

  const handleContractSelect = (contract: ContractType | null) => {
    if (!contract) return;
    setSelectedContract(contract);
    setShowTemplates(false);
    setStage(CreationStage.FILL_FORM);
  };

  const handleTemplateSelect = (template: ContractTemplate) => {
    setSelectedContract(template.contractType);
    setShowTemplates(false);

    const autoFillData = getAutoFillDefaults(
      template.contractType,
      undefined,
      template.defaultData
    );
    setFormData(autoFillData);

    if (isExpressMode && template.requiredFields.length <= 3) {
      setStage(CreationStage.BUILD_CLAUSES);
    } else {
      setStage(CreationStage.FILL_FORM);
    }
  };

  const handleFormComplete = (data: Record<string, any>) => {
    setFormData(data);
    setStage(CreationStage.BUILD_CLAUSES);
  };

  const handleAddClause = (clause: Clause) => {
    setClauses((prev) => (prev.some((c) => c.id === clause.id) ? prev : [...prev, clause]));
  };

  const handleRemoveClause = (clauseId: string) => {
    setClauses((prev) => prev.filter((c) => c.id !== clauseId));
  };

  const handleReorderClauses = (reordered: Clause[]) => {
    setClauses(reordered);
  };

  const handleBackToSelection = () => {
    setStage(CreationStage.SELECT_TYPE);
    setSelectedContract(null);
    setFormData({});
    setClauses([]);
    setShowTemplates(true);
  };

  const handleBackToForm = () => setStage(CreationStage.FILL_FORM);
  const handleClausesDone = () => setStage(CreationStage.PREVIEW);

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Top Header */}
      <header className="sticky top-0 z-10 backdrop-blur supports-[backdrop-filter]:bg-white/70 dark:supports-[backdrop-filter]:bg-dark-100/70 border-b border-gray-100 dark:border-dark-300">
        <div className="w-full px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-7 w-7 rounded-full bg-gray-900 dark:bg-white" />
            <div className="text-sm text-gray-600 dark:text-gray-300">Contract Generator</div>
          </div>
          <div className="w-64 hidden sm:block">
            <SimpleProgressBar currentStage={getSimpleStage(stage)} isExpressMode={true} />
          </div>
        </div>
      </header>

      {/* Main Canvas */}
      <main className="w-full px-4 py-10">
        {/* Stage Title */}
        <div className="mb-8">
          <h1 className="text-2xl font-semibold tracking-tight text-gray-900 dark:text-white">
            {stage === CreationStage.SELECT_TYPE && 'Start a new contract'}
            {stage === CreationStage.FILL_FORM && 'Details'}
            {stage === CreationStage.BUILD_CLAUSES && 'Clauses'}
            {stage === CreationStage.PREVIEW && 'Review & finalize'}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {stage === CreationStage.SELECT_TYPE && 'Choose a template or browse all types.'}
            {stage === CreationStage.FILL_FORM && 'Only essential fields. You can refine later.'}
            {stage === CreationStage.BUILD_CLAUSES && 'Add, remove, or reorder what matters.'}
            {stage === CreationStage.PREVIEW && 'Read comfortably. Sign when ready.'}
          </p>
        </div>

        <div className="rounded-2xl border border-gray-200 dark:border-dark-300 p-6">
          {stage === CreationStage.SELECT_TYPE && (
            <div className="space-y-6">
              {showTemplates ? (
                <>
                  <SmartTemplates onTemplateSelect={handleTemplateSelect} />
                  <div className="text-center">
                    <button
                      onClick={() => setShowTemplates(false)}
                      className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Or browse all contract types
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <div className="rounded-xl border border-gray-200 dark:border-dark-300 p-4">
                    <ContractGalaxy onSelectContract={handleContractSelect} />
                  </div>
                  <div className="text-center">
                    <button
                      onClick={() => setShowTemplates(true)}
                      className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Back to templates
                    </button>
                  </div>
                </>
              )}
            </div>
          )}

          {stage === CreationStage.FILL_FORM && (
            <div className="w-full">
              <EnhancedSmartForm
                contractType={selectedContract}
                onComplete={handleFormComplete}
                onBack={handleBackToSelection}
              />
            </div>
          )}

          {stage === CreationStage.BUILD_CLAUSES && (
            <div>
              <EnhancedAIClauseBuilder
                contractType={selectedContract}
                formData={formData}
                onAddClause={handleAddClause}
                onRemoveClause={handleRemoveClause}
                onReorderClauses={handleReorderClauses}
                existingClauses={clauses}
              />
              <div className="mt-6 flex items-center justify-end">
                <button
                  onClick={handleBackToForm}
                  className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mr-3"
                >
                  Edit details
                </button>
                <button
                  onClick={handleClausesDone}
                  className="px-4 py-2 text-sm rounded-md bg-gray-900 text-white hover:bg-black dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100"
                >
                  Continue
                </button>
              </div>
            </div>
          )}

          {stage === CreationStage.PREVIEW && (
            <div className="space-y-4">
              <ContractPreview
                contractType={selectedContract}
                formData={formData}
                clauses={clauses}
              />
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default MinimalContractGenerator;
