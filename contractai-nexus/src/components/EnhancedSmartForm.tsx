import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ContractType } from './ContractGalaxy';

interface FormField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'date' | 'select' | 'checkbox' | 'number' | 'email' | 'phone' | 'currency' | 'percentage';
  placeholder?: string;
  options?: { value: string; label: string }[];
  required?: boolean;
  description?: string;
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: any) => string | null;
  };
  suggestions?: string[];
  dependsOn?: string;
  showWhen?: (formData: Record<string, any>) => boolean;
  autoComplete?: string;
  icon?: string;
}

interface FormSection {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
}

interface SmartFormProps {
  contractType: ContractType | null;
  onComplete: (formData: Record<string, any>) => void;
  onBack: () => void;
  className?: string;
}

// Enhanced dynamic form sections with intelligent field definitions
const getFormSections = (contractType: ContractType | null): FormSection[] => {
  if (!contractType) return [];

  // Common party information section
  const partySection: FormSection = {
    id: 'parties',
    title: 'Party Information',
    description: 'Enter information about the parties involved in this contract.',
    fields: [
      {
        id: 'firstPartyName',
        label: 'First Party Name',
        type: 'text',
        placeholder: 'Enter company or individual name',
        required: true,
        icon: '🏢',
        autoComplete: 'organization',
        validation: {
          minLength: 2,
          maxLength: 100,
          custom: (value: string) => {
            if (value && !/^[a-zA-Z0-9\s\-.,&()]+$/.test(value)) {
              return 'Please use only letters, numbers, and common business characters';
            }
            return null;
          }
        },
        suggestions: ['Acme Corporation', 'Tech Solutions LLC', 'Global Industries Inc.']
      },
      {
        id: 'firstPartyEmail',
        label: 'First Party Email',
        type: 'email',
        placeholder: '<EMAIL>',
        required: true,
        icon: '📧',
        autoComplete: 'email',
        validation: {
          pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
          custom: (value: string) => {
            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              return 'Please enter a valid email address';
            }
            return null;
          }
        }
      },
      {
        id: 'firstPartyAddress',
        label: 'First Party Address',
        type: 'textarea',
        placeholder: 'Enter complete business address',
        required: true,
        icon: '📍',
        autoComplete: 'street-address',
        validation: {
          minLength: 10,
          maxLength: 500
        }
      },
      {
        id: 'secondPartyName',
        label: 'Second Party Name',
        type: 'text',
        placeholder: 'Enter company or individual name',
        required: true,
        icon: '👤',
        validation: {
          minLength: 2,
          maxLength: 100
        }
      },
      {
        id: 'secondPartyEmail',
        label: 'Second Party Email',
        type: 'email',
        placeholder: '<EMAIL>',
        required: true,
        icon: '📧',
        autoComplete: 'email',
        validation: {
          pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        }
      }
    ]
  };

  // Contract-specific sections based on type
  const contractSpecificSections: FormSection[] = [];

  switch (contractType.id) {
    case 'nda':
      contractSpecificSections.push({
        id: 'nda_terms',
        title: 'NDA Terms',
        description: 'Define the scope and terms of confidentiality.',
        fields: [
          {
            id: 'confidentialityPurpose',
            label: 'Purpose of Disclosure',
            type: 'textarea',
            placeholder: 'Describe the purpose for sharing confidential information',
            required: true,
            icon: '🎯',
            validation: {
              minLength: 20,
              maxLength: 1000
            }
          },
          {
            id: 'ndaDuration',
            label: 'Duration of Agreement',
            type: 'select',
            required: true,
            icon: '⏱️',
            options: [
              { value: '1', label: '1 Year' },
              { value: '2', label: '2 Years' },
              { value: '3', label: '3 Years' },
              { value: '5', label: '5 Years' },
              { value: 'indefinite', label: 'Indefinite' }
            ]
          },
          {
            id: 'mutualNDA',
            label: 'Mutual NDA',
            type: 'checkbox',
            description: 'Both parties will share confidential information',
            icon: '🔄'
          }
        ]
      });
      break;

    case 'employment':
      contractSpecificSections.push({
        id: 'employment_terms',
        title: 'Employment Terms',
        description: 'Define the employment relationship and compensation.',
        fields: [
          {
            id: 'jobTitle',
            label: 'Job Title',
            type: 'text',
            placeholder: 'Software Engineer, Marketing Manager, etc.',
            required: true,
            icon: '💼',
            validation: {
              minLength: 2,
              maxLength: 100
            }
          },
          {
            id: 'salary',
            label: 'Annual Salary',
            type: 'currency',
            placeholder: '75000',
            required: true,
            icon: '💰',
            validation: {
              min: 0,
              max: 10000000
            }
          },
          {
            id: 'startDate',
            label: 'Start Date',
            type: 'date',
            required: true,
            icon: '📅'
          },
          {
            id: 'employmentType',
            label: 'Employment Type',
            type: 'select',
            required: true,
            icon: '📋',
            options: [
              { value: 'full-time', label: 'Full-time' },
              { value: 'part-time', label: 'Part-time' },
              { value: 'contract', label: 'Contract' },
              { value: 'temporary', label: 'Temporary' }
            ]
          }
        ]
      });
      break;

    case 'service':
      contractSpecificSections.push({
        id: 'service_terms',
        title: 'Service Terms',
        description: 'Define the services to be provided and payment terms.',
        fields: [
          {
            id: 'serviceDescription',
            label: 'Service Description',
            type: 'textarea',
            placeholder: 'Detailed description of services to be provided',
            required: true,
            icon: '🛠️',
            validation: {
              minLength: 50,
              maxLength: 2000
            }
          },
          {
            id: 'serviceRate',
            label: 'Service Rate',
            type: 'currency',
            placeholder: '150',
            required: true,
            icon: '💵',
            validation: {
              min: 0,
              max: 100000
            }
          },
          {
            id: 'rateType',
            label: 'Rate Type',
            type: 'select',
            required: true,
            icon: '⏰',
            options: [
              { value: 'hourly', label: 'Per Hour' },
              { value: 'daily', label: 'Per Day' },
              { value: 'weekly', label: 'Per Week' },
              { value: 'monthly', label: 'Per Month' },
              { value: 'project', label: 'Per Project' }
            ]
          },
          {
            id: 'projectDuration',
            label: 'Project Duration',
            type: 'text',
            placeholder: '3 months, 6 weeks, etc.',
            required: true,
            icon: '📆'
          }
        ]
      });
      break;

    case 'lease':
      contractSpecificSections.push({
        id: 'lease_terms',
        title: 'Lease Terms',
        description: 'Define the property lease terms and conditions.',
        fields: [
          {
            id: 'propertyAddress',
            label: 'Property Address',
            type: 'textarea',
            placeholder: 'Complete address of the property being leased',
            required: true,
            icon: '🏠',
            validation: {
              minLength: 10,
              maxLength: 500
            }
          },
          {
            id: 'monthlyRent',
            label: 'Monthly Rent',
            type: 'currency',
            placeholder: '2500',
            required: true,
            icon: '💰',
            validation: {
              min: 0,
              max: 100000
            }
          },
          {
            id: 'securityDeposit',
            label: 'Security Deposit',
            type: 'currency',
            placeholder: '2500',
            required: true,
            icon: '🔒',
            validation: {
              min: 0,
              max: 100000
            }
          },
          {
            id: 'leaseDuration',
            label: 'Lease Duration',
            type: 'select',
            required: true,
            icon: '📅',
            options: [
              { value: '6', label: '6 Months' },
              { value: '12', label: '1 Year' },
              { value: '24', label: '2 Years' },
              { value: '36', label: '3 Years' }
            ]
          }
        ]
      });
      break;

    case 'partnership':
      contractSpecificSections.push({
        id: 'partnership_terms',
        title: 'Partnership Terms',
        description: 'Define the partnership structure and responsibilities.',
        fields: [
          {
            id: 'partnershipType',
            label: 'Partnership Type',
            type: 'select',
            required: true,
            icon: '🤝',
            options: [
              { value: 'general', label: 'General Partnership' },
              { value: 'limited', label: 'Limited Partnership' },
              { value: 'llp', label: 'Limited Liability Partnership' },
              { value: 'joint-venture', label: 'Joint Venture' }
            ]
          },
          {
            id: 'profitSharing',
            label: 'Profit Sharing (%)',
            type: 'percentage',
            placeholder: '50',
            required: true,
            icon: '📊',
            validation: {
              min: 0,
              max: 100
            }
          },
          {
            id: 'partnershipPurpose',
            label: 'Partnership Purpose',
            type: 'textarea',
            placeholder: 'Describe the purpose and goals of the partnership',
            required: true,
            icon: '🎯',
            validation: {
              minLength: 50,
              maxLength: 1000
            }
          }
        ]
      });
      break;

    case 'custom':
      contractSpecificSections.push({
        id: 'custom_terms',
        title: 'Contract Terms',
        description: 'Define your custom contract terms and conditions.',
        fields: [
          {
            id: 'contractPurpose',
            label: 'Contract Purpose',
            type: 'textarea',
            placeholder: 'Describe the purpose and scope of this contract',
            required: true,
            icon: '📋',
            validation: {
              minLength: 50,
              maxLength: 2000
            }
          },
          {
            id: 'contractValue',
            label: 'Contract Value',
            type: 'currency',
            placeholder: '10000',
            icon: '💰',
            validation: {
              min: 0,
              max: 10000000
            }
          },
          {
            id: 'contractDuration',
            label: 'Contract Duration',
            type: 'text',
            placeholder: '12 months, 2 years, etc.',
            required: true,
            icon: '⏱️'
          }
        ]
      });
      break;
  }

  return [partySection, ...contractSpecificSections];
};

// Enhanced SmartForm Component
const EnhancedSmartForm: React.FC<SmartFormProps> = ({
  contractType,
  onComplete,
  onBack,
  className = '',
}) => {
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [suggestions, setSuggestions] = useState<Record<string, string[]>>({});
  const [showSuggestions, setShowSuggestions] = useState<Record<string, boolean>>({});
  const [savedProgress, setSavedProgress] = useState<boolean>(false);

  const formSections = getFormSections(contractType);
  const currentSection = formSections[currentSectionIndex];
  const isFirstSection = currentSectionIndex === 0;
  const isLastSection = currentSectionIndex === formSections.length - 1;
  const progress = ((currentSectionIndex + 1) / formSections.length) * 100;

  // Auto-save progress
  useEffect(() => {
    if (Object.keys(formData).length > 0) {
      const saveKey = `contract_form_${contractType?.id || 'unknown'}`;
      localStorage.setItem(saveKey, JSON.stringify({
        formData,
        currentSectionIndex,
        timestamp: Date.now()
      }));
      setSavedProgress(true);
      setTimeout(() => setSavedProgress(false), 2000);
    }
  }, [formData, currentSectionIndex, contractType?.id]);

  // Load saved progress
  useEffect(() => {
    if (contractType) {
      const saveKey = `contract_form_${contractType.id}`;
      const saved = localStorage.getItem(saveKey);
      if (saved) {
        try {
          const { formData: savedData, currentSectionIndex: savedIndex } = JSON.parse(saved);
          setFormData(savedData);
          setCurrentSectionIndex(savedIndex);
        } catch (error) {
          console.error('Failed to load saved progress:', error);
        }
      }
    }
  }, [contractType]);

  const validateField = useCallback((field: FormField, value: any): string | null => {
    if (field.required && (!value || value === '')) {
      return 'This field is required';
    }

    if (value && field.validation) {
      const { pattern, minLength, maxLength, min, max, custom } = field.validation;

      if (pattern && !pattern.test(value)) {
        return 'Please enter a valid format';
      }

      if (minLength && value.length < minLength) {
        return `Minimum ${minLength} characters required`;
      }

      if (maxLength && value.length > maxLength) {
        return `Maximum ${maxLength} characters allowed`;
      }

      if (min !== undefined && Number(value) < min) {
        return `Minimum value is ${min}`;
      }

      if (max !== undefined && Number(value) > max) {
        return `Maximum value is ${max}`;
      }

      if (custom) {
        const customError = custom(value);
        if (customError) return customError;
      }
    }

    return null;
  }, []);

  const handleInputChange = useCallback((fieldId: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [fieldId]: value,
    }));

    // Clear error when field is filled
    if (errors[fieldId]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }

    // Update suggestions based on input
    const field = currentSection?.fields.find(f => f.id === fieldId);
    if (field?.suggestions && value) {
      const filteredSuggestions = field.suggestions.filter(s => 
        s.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(prev => ({
        ...prev,
        [fieldId]: filteredSuggestions
      }));
    }
  }, [currentSection, errors]);

  const handleInputBlur = useCallback((fieldId: string) => {
    const field = currentSection?.fields.find(f => f.id === fieldId);
    if (!field) return;
    const error = validateField(field, formData[fieldId]);
    setErrors((prev) => ({
      ...prev,
      ...(error ? { [fieldId]: error } : (() => { const p = { ...prev }; delete p[fieldId]; return p; })()),
    }));
  }, [currentSection, formData, validateField]);

  const validateSection = useCallback(() => {
    const newErrors: Record<string, string> = {};
    
    currentSection.fields.forEach((field) => {
      if (field.showWhen && !field.showWhen(formData)) {
        return; // Skip validation for hidden fields
      }

      const error = validateField(field, formData[field.id]);
      if (error) {
        newErrors[field.id] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [currentSection, formData, validateField]);

  const handleNext = () => {
    if (validateSection()) {
      if (isLastSection) {
        onComplete(formData);
      } else {
        setCurrentSectionIndex(prev => prev + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (isFirstSection) {
      onBack();
    } else {
      setCurrentSectionIndex(prev => prev - 1);
    }
  };

  const renderField = (field: FormField) => {
    if (field.showWhen && !field.showWhen(formData)) {
      return null;
    }

    const value = formData[field.id] || '';
    const error = errors[field.id];
    const fieldSuggestions = suggestions[field.id] || [];
    const showFieldSuggestions = showSuggestions[field.id] && fieldSuggestions.length > 0;

    return (
      <motion.div
        key={field.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-2"
      >
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          <div className="flex items-center space-x-2">
            {field.icon && <span className="text-lg">{field.icon}</span>}
            <span>{field.label}</span>
            {field.required && <span className="text-red-500">*</span>}
          </div>
        </label>

        {field.description && (
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {field.description}
          </p>
        )}

        <div className="relative">
          {field.type === 'textarea' ? (
            <textarea
              value={value}
              onChange={(e) => handleInputChange(field.id, e.target.value)}
              onFocus={() => setShowSuggestions(prev => ({ ...prev, [field.id]: true }))}
              onBlur={() => { handleInputBlur(field.id); setTimeout(() => setShowSuggestions(prev => ({ ...prev, [field.id]: false })), 200); }}
              placeholder={field.placeholder}
              autoComplete={field.autoComplete}
              rows={4}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                error 
                  ? 'border-red-300 bg-red-50 dark:bg-red-900/20' 
                  : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800'
              }`}
            />
          ) : field.type === 'select' ? (
            <select
              value={value}
              onChange={(e) => handleInputChange(field.id, e.target.value)}
              onBlur={() => handleInputBlur(field.id)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                error 
                  ? 'border-red-300 bg-red-50 dark:bg-red-900/20' 
                  : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800'
              }`}
            >
              <option value="">Select an option</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          ) : field.type === 'checkbox' ? (
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={value || false}
                onChange={(e) => handleInputChange(field.id, e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {field.description || field.label}
              </span>
            </label>
          ) : (
            <input
              type={field.type === 'currency' || field.type === 'percentage' ? 'number' : field.type}
              value={value}
              onChange={(e) => handleInputChange(field.id, e.target.value)}
              onFocus={() => setShowSuggestions(prev => ({ ...prev, [field.id]: true }))}
              onBlur={() => { handleInputBlur(field.id); setTimeout(() => setShowSuggestions(prev => ({ ...prev, [field.id]: false })), 200); }}
              placeholder={field.placeholder}
              autoComplete={field.autoComplete}
              min={field.validation?.min}
              max={field.validation?.max}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                error 
                  ? 'border-red-300 bg-red-50 dark:bg-red-900/20' 
                  : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800'
              }`}
            />
          )}

          {/* Helper hints for numeric fields */}
          {!error && (field.type === 'currency' || field.type === 'percentage') && (
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {field.type === 'currency' ? 'Enter a numeric amount. Decimals allowed.' : 'Enter a value between 0 and 100.'}
            </p>
          )}

          {/* Suggestions dropdown */}
          <AnimatePresence>
            {showFieldSuggestions && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-40 overflow-y-auto"
              >
                {fieldSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => {
                      handleInputChange(field.id, suggestion);
                      setShowSuggestions(prev => ({ ...prev, [field.id]: false }));
                    }}
                    className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                  >
                    {suggestion}
                  </button>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {error && (
          <motion.p
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="text-sm text-red-600 dark:text-red-400"
          >
            {error}
          </motion.p>
        )}
      </motion.div>
    );
  };

  if (!contractType) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <p className="text-xl text-gray-600 dark:text-gray-300">
          Please select a contract type to continue.
        </p>
        <button
          onClick={onBack}
          className="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Progress Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {contractType.name} Contract
          </h2>
          {savedProgress && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center space-x-2 text-green-600 dark:text-green-400"
            >
              <span className="text-sm">✓ Progress saved</span>
            </motion.div>
          )}
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <motion.div
            className="bg-blue-600 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
        
        <div className="flex justify-between mt-2 text-sm text-gray-600 dark:text-gray-400">
          <span>Step {currentSectionIndex + 1} of {formSections.length}</span>
          <span>{Math.round(progress)}% complete</span>
        </div>
      </div>

      {/* Current Section */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSectionIndex}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8"
        >
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {currentSection.title}
            </h3>
            {currentSection.description && (
              <p className="text-gray-600 dark:text-gray-300">
                {currentSection.description}
              </p>
            )}
          </div>

          <div className="space-y-6">
            {currentSection.fields.map(renderField)}
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex justify-between mt-8">
        <button
          onClick={handlePrevious}
          className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
        >
          ← {isFirstSection ? 'Back to Selection' : 'Previous'}
        </button>

        <button
          onClick={handleNext}
          className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"
        >
          {isLastSection ? 'Continue to Clauses' : 'Next'} →
        </button>
      </div>
    </div>
  );
};

export default EnhancedSmartForm;
