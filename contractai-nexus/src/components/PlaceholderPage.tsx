import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowLeft, Mail } from 'lucide-react';

interface PlaceholderPageProps {
  title: string;
  description: string;
  emoji: string;
  comingSoon?: boolean;
}

const PlaceholderPage: React.FC<PlaceholderPageProps> = ({ 
  title, 
  description, 
  emoji, 
  comingSoon = true 
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-dark-100 dark:to-dark-200">
      <div className="max-w-2xl mx-auto text-center px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="text-8xl mb-8">{emoji}</div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {title}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            {description}
          </p>
          
          {comingSoon && (
            <div className="bg-white dark:bg-dark-200 rounded-2xl p-8 mb-8 shadow-lg">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Coming Soon!
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We're working hard to bring you this feature. Stay tuned for updates!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/contact"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-lg hover:from-primary-600 hover:to-secondary-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Mail className="w-5 h-5 mr-2" />
                  Get Notified
                </Link>
                <Link
                  to="/"
                  className="inline-flex items-center px-6 py-3 bg-gray-200 dark:bg-dark-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-dark-400 transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Back to Home
                </Link>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default PlaceholderPage;
