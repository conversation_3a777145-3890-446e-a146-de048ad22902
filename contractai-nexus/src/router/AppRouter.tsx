import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '../contexts/ThemeContext';
import { ExpressModeProvider } from '../contexts/ExpressModeContext';

// Layout Components
import Layout from '../components/Layout';

// Import all pages from the index file
import {
  HomePage,
  AboutPage,
  ContactPage,
  PricingPage,
  FeaturesPage,
  TemplatesPage,
  IntegrationsPage,
  APIPage,
  CareersPage,
  PressPage,
  BlogPage,
  DocumentationPage,
  HelpCenterPage,
  CommunityPage,
  WebinarsPage,
  CaseStudiesPage,
  PrivacyPolicyPage,
  TermsOfServicePage,
  CookiePolicyPage,
  GDPRPage,
  SecurityPage,
  ContractCreatorPage,
  DashboardPage,
  NotFoundPage
} from '../pages';

const AppRouter: React.FC = () => {
  return (
    <ThemeProvider>
      <ExpressModeProvider>
        <Router>
          <Routes>
            {/* Main Pages */}
            <Route path="/" element={<Layout><HomePage /></Layout>} />
            <Route path="/about" element={<Layout><AboutPage /></Layout>} />
            <Route path="/contact" element={<Layout><ContactPage /></Layout>} />
            <Route path="/pricing" element={<Layout><PricingPage /></Layout>} />
            <Route path="/features" element={<Layout><FeaturesPage /></Layout>} />
            
            {/* Product Pages */}
            <Route path="/templates" element={<Layout><TemplatesPage /></Layout>} />
            <Route path="/integrations" element={<Layout><IntegrationsPage /></Layout>} />
            <Route path="/api" element={<Layout><APIPage /></Layout>} />
            
            {/* Company Pages */}
            <Route path="/careers" element={<Layout><CareersPage /></Layout>} />
            <Route path="/press" element={<Layout><PressPage /></Layout>} />
            <Route path="/blog" element={<Layout><BlogPage /></Layout>} />
            
            {/* Resources Pages */}
            <Route path="/docs" element={<Layout><DocumentationPage /></Layout>} />
            <Route path="/help" element={<Layout><HelpCenterPage /></Layout>} />
            <Route path="/community" element={<Layout><CommunityPage /></Layout>} />
            <Route path="/webinars" element={<Layout><WebinarsPage /></Layout>} />
            <Route path="/cases" element={<Layout><CaseStudiesPage /></Layout>} />
            
            {/* Legal Pages */}
            <Route path="/privacy" element={<Layout><PrivacyPolicyPage /></Layout>} />
            <Route path="/terms" element={<Layout><TermsOfServicePage /></Layout>} />
            <Route path="/cookies" element={<Layout><CookiePolicyPage /></Layout>} />
            <Route path="/gdpr" element={<Layout><GDPRPage /></Layout>} />
            <Route path="/security" element={<Layout><SecurityPage /></Layout>} />
            
            {/* App Pages */}
            <Route path="/create" element={<Layout><ContractCreatorPage /></Layout>} />
            <Route path="/dashboard" element={<Layout><DashboardPage /></Layout>} />
            
            {/* 404 Page */}
            <Route path="*" element={<Layout><NotFoundPage /></Layout>} />
          </Routes>
        </Router>
      </ExpressModeProvider>
    </ThemeProvider>
  );
};

export default AppRouter;
