import { ContractType, contractTypes } from '../components/ContractGalaxy';

export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  contractType: ContractType;
  icon: string;
  estimatedTime: string;
  defaultData: Record<string, any>;
  requiredFields: string[];
  isPopular?: boolean;
}

// Helper function to get contract type by ID
const getContractTypeById = (id: string): ContractType => {
  return contractTypes.find(type => type.id === id) || contractTypes[0];
};

export const contractTemplates: ContractTemplate[] = [
  {
    id: 'quick-nda',
    name: 'Quick NDA',
    description: 'Simple non-disclosure agreement for protecting confidential information',
    contractType: getContractTypeById('nda'),
    icon: '🔒',
    estimatedTime: '2 minutes',
    isPopular: true,
    defaultData: {
      disclosurePurpose: 'Business discussions and potential collaboration',
      confidentialityPeriod: '2 years',
      returnOfMaterials: true,
      jurisdiction: 'United States',
      mutualDisclosure: false,
      remedies: 'Injunctive relief and monetary damages',
    },
    requiredFields: ['disclosingParty', 'receivingParty', 'effectiveDate']
  },
  {
    id: 'freelancer-agreement',
    name: 'Freelancer Agreement',
    description: 'Standard agreement for hiring freelancers and contractors',
    contractType: getContractTypeById('employment'),
    icon: '💼',
    estimatedTime: '3 minutes',
    isPopular: true,
    defaultData: {
      employmentType: 'contractor',
      workLocation: 'Remote',
      paymentTerms: 'Net 30 days',
      intellectualProperty: 'Work for hire - all rights to client',
      terminationNotice: '14 days',
      confidentiality: true,
      currency: 'USD',
    },
    requiredFields: ['contractorName', 'clientName', 'projectDescription', 'compensation', 'deadline']
  },
  {
    id: 'simple-service',
    name: 'Simple Service Contract',
    description: 'Basic service agreement for ongoing business services',
    contractType: getContractTypeById('service'),
    icon: '🤝',
    estimatedTime: '4 minutes',
    isPopular: true,
    defaultData: {
      serviceType: 'Professional services',
      paymentSchedule: 'Monthly',
      paymentTerms: 'Net 30 days',
      autoRenewal: true,
      renewalPeriod: '12 months',
      terminationNotice: '30 days',
      liability: 'Limited to contract value',
      currency: 'USD',
    },
    requiredFields: ['serviceProvider', 'client', 'serviceDescription', 'monthlyFee', 'startDate']
  },
  {
    id: 'rental-lease',
    name: 'Property Rental',
    description: 'Standard residential or commercial lease agreement',
    contractType: getContractTypeById('lease'),
    icon: '🏠',
    estimatedTime: '5 minutes',
    defaultData: {
      leaseType: 'Fixed-term',
      leaseDuration: '12 months',
      paymentFrequency: 'Monthly',
      securityDeposit: 'One month rent',
      petPolicy: 'No pets allowed',
      maintenanceResponsibility: 'Landlord for major repairs, tenant for minor',
      renewalOption: true,
      currency: 'USD',
    },
    requiredFields: ['landlord', 'tenant', 'propertyAddress', 'monthlyRent', 'startDate']
  },
  {
    id: 'partnership-agreement',
    name: 'Business Partnership',
    description: 'Partnership agreement for business collaboration',
    contractType: getContractTypeById('partnership'),
    icon: '🤝',
    estimatedTime: '6 minutes',
    defaultData: {
      partnershipType: 'General Partnership',
      profitSharingRatio: '50/50',
      decisionMaking: 'Unanimous consent for major decisions',
      capitalContribution: 'Equal contributions',
      withdrawalNotice: '90 days',
      disputeResolution: 'Mediation then arbitration',
      termination: 'Mutual agreement or cause',
    },
    requiredFields: ['partner1Name', 'partner2Name', 'businessPurpose', 'initialCapital', 'effectiveDate']
  },
  {
    id: 'consulting-agreement',
    name: 'Consulting Agreement',
    description: 'Professional consulting services contract',
    contractType: getContractTypeById('service'),
    icon: '📊',
    estimatedTime: '4 minutes',
    defaultData: {
      consultingType: 'Professional advisory services',
      paymentStructure: 'Hourly rate',
      paymentTerms: 'Net 15 days',
      expenseReimbursement: true,
      intellectualProperty: 'Client owns work product',
      confidentiality: true,
      terminationClause: 'Either party with 30 days notice',
      currency: 'USD',
    },
    requiredFields: ['consultant', 'client', 'scopeOfWork', 'hourlyRate', 'startDate']
  }
];

export const getTemplatesByPopularity = () => {
  return contractTemplates.filter(template => template.isPopular);
};

export const getTemplateById = (id: string) => {
  return contractTemplates.find(template => template.id === id);
};

export const getTemplatesByType = (contractType: ContractType) => {
  return contractTemplates.filter(template => template.contractType === contractType);
};
