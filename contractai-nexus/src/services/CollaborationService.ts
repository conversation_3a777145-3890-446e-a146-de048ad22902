import { ContractType } from '../components/ContractGalaxy';
import { Clause } from '../types/clauses';

// Types for collaboration
export interface CollaborationUser {
  id: string;
  name: string;
  color: string;
  avatar?: string;
  isActive: boolean;
  lastActivity: Date;
}

export interface CollaborationSession {
  id: string;
  contractId: string;
  users: CollaborationUser[];
  contractType: ContractType | null;
  formData: Record<string, any>;
  clauses: Clause[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CollaborationUpdate {
  type: 'form' | 'clause' | 'user_joined' | 'user_left' | 'user_cursor';
  userId: string;
  data: any;
  timestamp: Date;
}

// Mock colors for collaboration users
const USER_COLORS = [
  '#FF5733', // Red-Orange
  '#33A8FF', // Blue
  '#33FF57', // Green
  '#F033FF', // Purple
  '#FFBD33', // Yellow-Orange
  '#33FFF3', // Cyan
  '#FF33A8', // Pink
  '#8C33FF', // Violet
];

// Mock avatars (would be replaced with real user avatars in a production app)
const MOCK_AVATARS = [
  '👩‍💼', '👨‍💼', '👩‍⚖️', '👨‍⚖️', '👩‍💻', '👨‍💻', '👩‍🔧', '👨‍🔧'
];

// Mock implementation of a collaboration service
class CollaborationService {
  private static instance: CollaborationService;
  private sessions: Map<string, CollaborationSession>;
  private callbacks: Map<string, ((update: CollaborationUpdate) => void)[]>;
  private currentUser: CollaborationUser | null = null;
  
  private constructor() {
    this.sessions = new Map();
    this.callbacks = new Map();
    
    // Generate a random user ID and name for this session
    const userId = `user_${Math.random().toString(36).substring(2, 9)}`;
    const randomColorIndex = Math.floor(Math.random() * USER_COLORS.length);
    const randomAvatarIndex = Math.floor(Math.random() * MOCK_AVATARS.length);
    
    this.currentUser = {
      id: userId,
      name: `User ${Math.floor(Math.random() * 1000)}`,
      color: USER_COLORS[randomColorIndex],
      avatar: MOCK_AVATARS[randomAvatarIndex],
      isActive: true,
      lastActivity: new Date()
    };
  }
  
  public static getInstance(): CollaborationService {
    if (!CollaborationService.instance) {
      CollaborationService.instance = new CollaborationService();
    }
    return CollaborationService.instance;
  }
  
  public getCurrentUser(): CollaborationUser | null {
    return this.currentUser;
  }
  
  public setCurrentUserName(name: string): void {
    if (this.currentUser) {
      this.currentUser.name = name;
    }
  }
  
  // Create or join a collaboration session
  public joinSession(
    contractId: string,
    contractType: ContractType | null,
    formData: Record<string, any>,
    clauses: Clause[]
  ): CollaborationSession {
    if (!this.currentUser) {
      throw new Error('No current user set');
    }
    
    let session = this.sessions.get(contractId);
    
    if (!session) {
      // Create a new session if one doesn't exist
      session = {
        id: `session_${Math.random().toString(36).substring(2, 9)}`,
        contractId,
        users: [this.currentUser],
        contractType,
        formData,
        clauses,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      this.sessions.set(contractId, session);
    } else {
      // Add the current user to the existing session if they're not already in it
      if (!session.users.some(user => user.id === this.currentUser?.id)) {
        session.users.push(this.currentUser);
        session.updatedAt = new Date();
      }
      
      // Update the user's active status
      session.users = session.users.map(user => 
        user.id === this.currentUser?.id 
          ? { ...this.currentUser, isActive: true, lastActivity: new Date() }
          : user
      );
    }
    
    // Notify other users that this user has joined
    this.broadcastUpdate(contractId, {
      type: 'user_joined',
      userId: this.currentUser.id,
      data: this.currentUser,
      timestamp: new Date()
    });
    
    return session;
  }
  
  // Leave a collaboration session
  public leaveSession(contractId: string): void {
    if (!this.currentUser) {
      return;
    }
    
    const session = this.sessions.get(contractId);
    if (!session) {
      return;
    }
    
    // Update the user's status to inactive
    session.users = session.users.map(user => 
      user.id === this.currentUser?.id 
        ? { ...user, isActive: false, lastActivity: new Date() }
        : user
    );
    
    // Remove users who have been inactive for more than 30 minutes
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    session.users = session.users.filter(user => 
      user.isActive || user.lastActivity > thirtyMinutesAgo
    );
    
    // Notify other users that this user has left
    this.broadcastUpdate(contractId, {
      type: 'user_left',
      userId: this.currentUser.id,
      data: this.currentUser.id,
      timestamp: new Date()
    });
    
    // If no active users remain, clean up the session
    if (!session.users.some(user => user.isActive)) {
      // In a real implementation, we might persist the session to a database here
      // For now, we'll just keep it in memory
    }
  }
  
  // Get the current state of a session
  public getSession(contractId: string): CollaborationSession | null {
    return this.sessions.get(contractId) || null;
  }
  
  // Update form data in a session
  public updateFormData(contractId: string, formData: Record<string, any>): void {
    if (!this.currentUser) {
      return;
    }
    
    const session = this.sessions.get(contractId);
    if (!session) {
      return;
    }
    
    session.formData = { ...session.formData, ...formData };
    session.updatedAt = new Date();
    
    // Broadcast the update to all listeners
    this.broadcastUpdate(contractId, {
      type: 'form',
      userId: this.currentUser.id,
      data: formData,
      timestamp: new Date()
    });
  }
  
  // Update clauses in a session
  public updateClauses(contractId: string, clauses: Clause[]): void {
    if (!this.currentUser) {
      return;
    }
    
    const session = this.sessions.get(contractId);
    if (!session) {
      return;
    }
    
    session.clauses = clauses;
    session.updatedAt = new Date();
    
    // Broadcast the update to all listeners
    this.broadcastUpdate(contractId, {
      type: 'clause',
      userId: this.currentUser.id,
      data: clauses,
      timestamp: new Date()
    });
  }
  
  // Update user cursor position (for collaborative editing)
  public updateCursorPosition(contractId: string, position: { x: number, y: number }): void {
    if (!this.currentUser) {
      return;
    }
    
    // Broadcast the cursor position to all listeners
    this.broadcastUpdate(contractId, {
      type: 'user_cursor',
      userId: this.currentUser.id,
      data: position,
      timestamp: new Date()
    });
  }
  
  // Subscribe to updates for a specific contract
  public subscribeToUpdates(
    contractId: string,
    callback: (update: CollaborationUpdate) => void
  ): () => void {
    if (!this.callbacks.has(contractId)) {
      this.callbacks.set(contractId, []);
    }
    
    const callbacksForContract = this.callbacks.get(contractId)!;
    callbacksForContract.push(callback);
    
    // Return an unsubscribe function
    return () => {
      const index = callbacksForContract.indexOf(callback);
      if (index !== -1) {
        callbacksForContract.splice(index, 1);
      }
    };
  }
  
  // Broadcast an update to all subscribers for a contract
  private broadcastUpdate(contractId: string, update: CollaborationUpdate): void {
    const callbacksForContract = this.callbacks.get(contractId);
    if (!callbacksForContract) {
      return;
    }
    
    // In a real implementation, this would send the update to a server
    // which would then broadcast it to all connected clients
    // For now, we'll just call the callbacks directly with a small delay
    // to simulate network latency
    setTimeout(() => {
      callbacksForContract.forEach(callback => {
        try {
          callback(update);
        } catch (error) {
          console.error('Error in collaboration update callback:', error);
        }
      });
    }, Math.random() * 200); // Random delay between 0-200ms
  }
  
  // Simulate other users joining and making changes (for demo purposes)
  public simulateCollaboration(contractId: string): (() => void) {
    const session = this.sessions.get(contractId);
    if (!session) {
      // Return a no-op cleanup function if no session exists
      return () => {};
    }
    
    // Simulate a new user joining
    const simulateNewUser = () => {
      const userId = `sim_user_${Math.random().toString(36).substring(2, 9)}`;
      const randomColorIndex = Math.floor(Math.random() * USER_COLORS.length);
      const randomAvatarIndex = Math.floor(Math.random() * MOCK_AVATARS.length);
      
      const simulatedUser: CollaborationUser = {
        id: userId,
        name: `Simulated User ${Math.floor(Math.random() * 1000)}`,
        color: USER_COLORS[randomColorIndex],
        avatar: MOCK_AVATARS[randomAvatarIndex],
        isActive: true,
        lastActivity: new Date()
      };
      
      session.users.push(simulatedUser);
      
      this.broadcastUpdate(contractId, {
        type: 'user_joined',
        userId: simulatedUser.id,
        data: simulatedUser,
        timestamp: new Date()
      });
      
      // Simulate the user making changes
      const simulateUserActivity = () => {
        // Simulate cursor movement
        this.broadcastUpdate(contractId, {
          type: 'user_cursor',
          userId: simulatedUser.id,
          data: {
            x: Math.floor(Math.random() * 800),
            y: Math.floor(Math.random() * 600)
          },
          timestamp: new Date()
        });
        
        // Occasionally simulate the user leaving
        if (Math.random() < 0.1) {
          simulatedUser.isActive = false;
          simulatedUser.lastActivity = new Date();
          
          this.broadcastUpdate(contractId, {
            type: 'user_left',
            userId: simulatedUser.id,
            data: simulatedUser.id,
            timestamp: new Date()
          });
          
          return; // Stop simulating this user
        }
        
        // Schedule the next activity
        setTimeout(simulateUserActivity, Math.random() * 5000 + 1000);
      };
      
      // Start simulating user activity
      setTimeout(simulateUserActivity, Math.random() * 2000 + 1000);
    };
    
    // Simulate a new user joining every 10-30 seconds
    const simulateNewUserInterval = setInterval(() => {
      if (session.users.length < 5) { // Limit to 5 simulated users
        simulateNewUser();
      } else {
        clearInterval(simulateNewUserInterval);
      }
    }, Math.random() * 20000 + 10000);
    
    // Return a cleanup function
    return () => {
      clearInterval(simulateNewUserInterval);
    };
  }
}

export default CollaborationService;
