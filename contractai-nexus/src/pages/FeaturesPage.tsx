import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Shield, Users, Brain, Clock, FileText, CheckCircle, Globe } from 'lucide-react';

const FeaturesPage: React.FC = () => {
  const features = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: 'AI-Powered Generation',
      description: 'Advanced AI creates legally sound contracts tailored to your specific needs in minutes.',
      benefits: ['Natural language processing', 'Context-aware generation', 'Legal compliance built-in']
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: 'Lightning Fast',
      description: 'Create professional contracts in under 5 minutes with our streamlined workflow.',
      benefits: ['Quick templates', 'Smart defaults', 'One-click generation']
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Legally Compliant',
      description: 'All contracts are reviewed by legal experts and updated with latest regulations.',
      benefits: ['Expert legal review', 'Regulatory compliance', 'Industry standards']
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Team Collaboration',
      description: 'Work together on contracts with real-time collaboration and version control.',
      benefits: ['Real-time editing', 'Comment system', 'Version history']
    },
    {
      icon: <FileText className="w-8 h-8" />,
      title: 'Rich Templates',
      description: 'Extensive library of professionally crafted templates for every business need.',
      benefits: ['50+ templates', 'Industry-specific', 'Customizable clauses']
    },
    {
      icon: <CheckCircle className="w-8 h-8" />,
      title: 'Digital Signatures',
      description: 'Built-in e-signature functionality for seamless contract execution.',
      benefits: ['Legally binding', 'Multi-party signing', 'Audit trail']
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: 'Version Control',
      description: 'Track changes, compare versions, and maintain complete contract history.',
      benefits: ['Change tracking', 'Rollback capability', 'Audit logs']
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: 'Global Compliance',
      description: 'Support for multiple jurisdictions and international contract standards.',
      benefits: ['Multi-jurisdiction', 'GDPR compliant', 'International standards']
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-dark-100 dark:to-dark-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-6">
              Powerful Features
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Discover all the tools and capabilities that make ContractAI Nexus the most advanced 
              contract creation platform available today.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 bg-white dark:bg-dark-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 dark:bg-dark-200 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="text-primary-600 dark:text-primary-400 mb-6">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  {feature.description}
                </p>
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary-600 to-secondary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Experience All Features Today
            </h2>
            <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
              Start your free trial and discover how ContractAI Nexus can transform your contract creation process.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-white text-primary-600 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200 shadow-lg"
            >
              Start Free Trial
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default FeaturesPage;
