import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  Plus, 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  TrendingUp, 
  Users, 
  Download,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';

const DashboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'contracts' | 'templates'>('overview');

  const stats = [
    {
      label: 'Total Contracts',
      value: '24',
      change: '+12%',
      changeType: 'positive' as const,
      icon: <FileText className="w-6 h-6" />
    },
    {
      label: 'Active Contracts',
      value: '18',
      change: '+8%',
      changeType: 'positive' as const,
      icon: <CheckCircle className="w-6 h-6" />
    },
    {
      label: 'Pending Review',
      value: '3',
      change: '-2',
      changeType: 'neutral' as const,
      icon: <Clock className="w-6 h-6" />
    },
    {
      label: 'This Month',
      value: '7',
      change: '+40%',
      changeType: 'positive' as const,
      icon: <TrendingUp className="w-6 h-6" />
    }
  ];

  const recentContracts = [
    {
      id: '1',
      title: 'Freelancer Agreement - John Doe',
      type: 'Freelancer Agreement',
      status: 'completed',
      createdAt: '2024-01-15',
      lastModified: '2024-01-16'
    },
    {
      id: '2',
      title: 'NDA - TechCorp Inc.',
      type: 'Non-Disclosure Agreement',
      status: 'pending',
      createdAt: '2024-01-14',
      lastModified: '2024-01-14'
    },
    {
      id: '3',
      title: 'Service Contract - Marketing Agency',
      type: 'Service Agreement',
      status: 'draft',
      createdAt: '2024-01-13',
      lastModified: '2024-01-15'
    },
    {
      id: '4',
      title: 'Partnership Agreement - StartupXYZ',
      type: 'Partnership Agreement',
      status: 'completed',
      createdAt: '2024-01-12',
      lastModified: '2024-01-13'
    },
    {
      id: '5',
      title: 'Employment Contract - Jane Smith',
      type: 'Employment Agreement',
      status: 'pending',
      createdAt: '2024-01-11',
      lastModified: '2024-01-12'
    }
  ];

  const templates = [
    {
      id: '1',
      name: 'Quick NDA',
      description: 'Standard non-disclosure agreement',
      uses: 12,
      lastUsed: '2024-01-15'
    },
    {
      id: '2',
      name: 'Freelancer Agreement',
      description: 'Comprehensive freelancer contract',
      uses: 8,
      lastUsed: '2024-01-14'
    },
    {
      id: '3',
      name: 'Service Contract',
      description: 'Professional service agreement',
      uses: 6,
      lastUsed: '2024-01-13'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'draft':
        return <Edit className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Manage your contracts and track your progress
            </p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="mt-4 sm:mt-0"
          >
            <Link
              to="/create"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-lg hover:from-primary-600 hover:to-secondary-600 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <Plus className="w-5 h-5 mr-2" />
              Create New Contract
            </Link>
          </motion.div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="bg-white dark:bg-dark-200 rounded-2xl p-6 shadow-sm hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="text-primary-600 dark:text-primary-400">
                  {stat.icon}
                </div>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                  stat.changeType === 'positive' 
                    ? 'text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/30'
                    : 'text-gray-700 bg-gray-100 dark:text-gray-300 dark:bg-gray-900/30'
                }`}>
                  {stat.change}
                </span>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {stat.value}
              </div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-dark-300">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Overview' },
                { id: 'contracts', label: 'Recent Contracts' },
                { id: 'templates', label: 'Templates' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Recent Activity */}
              <div className="bg-white dark:bg-dark-200 rounded-2xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Recent Activity
                </h3>
                <div className="space-y-4">
                  {recentContracts.slice(0, 3).map((contract) => (
                    <div key={contract.id} className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {getStatusIcon(contract.status)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                          {contract.title}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {contract.lastModified}
                        </p>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(contract.status)}`}>
                        {contract.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-dark-200 rounded-2xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <Link
                    to="/create"
                    className="flex items-center p-3 bg-gray-50 dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors duration-200"
                  >
                    <Plus className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" />
                    <span className="text-gray-900 dark:text-gray-100">Create New Contract</span>
                  </Link>
                  <Link
                    to="/templates"
                    className="flex items-center p-3 bg-gray-50 dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors duration-200"
                  >
                    <FileText className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" />
                    <span className="text-gray-900 dark:text-gray-100">Browse Templates</span>
                  </Link>
                  <Link
                    to="/help"
                    className="flex items-center p-3 bg-gray-50 dark:bg-dark-300 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-400 transition-colors duration-200"
                  >
                    <Users className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" />
                    <span className="text-gray-900 dark:text-gray-100">Get Help</span>
                  </Link>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'contracts' && (
            <div className="bg-white dark:bg-dark-200 rounded-2xl shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-dark-300">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  All Contracts
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-dark-300">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Contract
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-dark-300">
                    {recentContracts.map((contract) => (
                      <tr key={contract.id} className="hover:bg-gray-50 dark:hover:bg-dark-300/50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {contract.title}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {contract.type}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(contract.status)}`}>
                            {contract.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {contract.createdAt}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button className="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300">
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300">
                              <Download className="w-4 h-4" />
                            </button>
                            <button className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'templates' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {templates.map((template) => (
                <div key={template.id} className="bg-white dark:bg-dark-200 rounded-2xl p-6 shadow-sm hover:shadow-lg transition-shadow duration-300">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {template.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    {template.description}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <span>Used {template.uses} times</span>
                    <span>Last used {template.lastUsed}</span>
                  </div>
                  <Link
                    to="/create"
                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200"
                  >
                    Use Template
                  </Link>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardPage;
