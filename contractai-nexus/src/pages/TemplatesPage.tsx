import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FileText, Clock, Users, Star, Search, Filter, ArrowRight } from 'lucide-react';

const TemplatesPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('popularity'); // popularity, time, alphabetical

  const templates = [
    // Legal Protection
    {
      id: '1',
      name: 'Quick NDA',
      description: 'Standard non-disclosure agreement for protecting confidential information',
      category: 'Legal Protection',
      estimatedTime: '2 minutes',
      popularity: 'Most Popular',
      icon: '🔒',
      features: ['Mutual/One-way options', 'Customizable terms', 'Industry-specific clauses'],
      tags: ['confidentiality', 'secrets', 'protection', 'startup']
    },
    {
      id: '2',
      name: 'Non-Compete Agreement',
      description: 'Prevent employees from competing after employment ends',
      category: 'Legal Protection',
      estimatedTime: '3 minutes',
      popularity: 'Popular',
      icon: '🚫',
      features: ['Geographic restrictions', 'Time limitations', 'Industry scope'],
      tags: ['competition', 'employee', 'restriction', 'business']
    },
    {
      id: '3',
      name: 'Intellectual Property Assignment',
      description: 'Transfer ownership of intellectual property rights',
      category: 'Legal Protection',
      estimatedTime: '4 minutes',
      popularity: 'Common',
      icon: '💡',
      features: ['Patent rights', 'Copyright transfer', 'Trade secrets'],
      tags: ['IP', 'patents', 'copyright', 'invention']
    },
    {
      id: '4',
      name: 'Data Processing Agreement',
      description: 'GDPR-compliant data processing terms for vendors',
      category: 'Legal Protection',
      estimatedTime: '5 minutes',
      popularity: 'Growing',
      icon: '🛡️',
      features: ['GDPR compliance', 'Data security', 'Processing purposes'],
      tags: ['GDPR', 'privacy', 'data', 'compliance']
    },

    // Employment
    {
      id: '5',
      name: 'Freelancer Agreement',
      description: 'Comprehensive contract for freelance work and independent contractors',
      category: 'Employment',
      estimatedTime: '3 minutes',
      popularity: 'Most Popular',
      icon: '💼',
      features: ['Payment terms', 'Scope of work', 'Intellectual property'],
      tags: ['freelance', 'contractor', 'gig', 'independent']
    },
    {
      id: '6',
      name: 'Employment Contract',
      description: 'Full-time employment agreement with comprehensive terms',
      category: 'Employment',
      estimatedTime: '5 minutes',
      popularity: 'Popular',
      icon: '👥',
      features: ['Compensation details', 'Benefits package', 'Termination procedures'],
      tags: ['employee', 'fulltime', 'salary', 'benefits']
    },
    {
      id: '7',
      name: 'Internship Agreement',
      description: 'Structured internship program with learning objectives',
      category: 'Employment',
      estimatedTime: '3 minutes',
      popularity: 'Common',
      icon: '🎓',
      features: ['Learning goals', 'Supervision structure', 'Evaluation criteria'],
      tags: ['intern', 'student', 'training', 'education']
    },
    {
      id: '8',
      name: 'Part-Time Employment',
      description: 'Flexible part-time work arrangement contract',
      category: 'Employment',
      estimatedTime: '4 minutes',
      popularity: 'Common',
      icon: '⏰',
      features: ['Flexible hours', 'Pro-rated benefits', 'Schedule requirements'],
      tags: ['parttime', 'flexible', 'hourly', 'schedule']
    },
    {
      id: '9',
      name: 'Remote Work Agreement',
      description: 'Terms for remote and hybrid work arrangements',
      category: 'Employment',
      estimatedTime: '4 minutes',
      popularity: 'Growing',
      icon: '🏠',
      features: ['Home office requirements', 'Communication protocols', 'Equipment provision'],
      tags: ['remote', 'hybrid', 'telecommute', 'home']
    },

    // Business Operations
    {
      id: '10',
      name: 'Service Contract',
      description: 'Professional service agreement for ongoing business relationships',
      category: 'Business Operations',
      estimatedTime: '4 minutes',
      popularity: 'Most Popular',
      icon: '🤝',
      features: ['Service levels', 'Termination clauses', 'Liability protection'],
      tags: ['service', 'business', 'ongoing', 'professional']
    },
    {
      id: '11',
      name: 'Partnership Agreement',
      description: 'Business partnership contract for joint ventures and collaborations',
      category: 'Business Operations',
      estimatedTime: '6 minutes',
      popularity: 'Popular',
      icon: '🤝',
      features: ['Profit sharing', 'Decision making', 'Exit strategies'],
      tags: ['partnership', 'joint venture', 'collaboration', 'business']
    },
    {
      id: '12',
      name: 'Vendor Agreement',
      description: 'Terms for ongoing vendor and supplier relationships',
      category: 'Business Operations',
      estimatedTime: '5 minutes',
      popularity: 'Popular',
      icon: '📦',
      features: ['Delivery terms', 'Quality standards', 'Payment schedules'],
      tags: ['vendor', 'supplier', 'procurement', 'delivery']
    },
    {
      id: '13',
      name: 'Distribution Agreement',
      description: 'Contract for product distribution and sales partnerships',
      category: 'Business Operations',
      estimatedTime: '7 minutes',
      popularity: 'Common',
      icon: '🚚',
      features: ['Territory rights', 'Sales targets', 'Marketing support'],
      tags: ['distribution', 'sales', 'territory', 'marketing']
    },
    {
      id: '14',
      name: 'Licensing Agreement',
      description: 'License intellectual property or technology to others',
      category: 'Business Operations',
      estimatedTime: '6 minutes',
      popularity: 'Common',
      icon: '📜',
      features: ['Usage rights', 'Royalty terms', 'Territory restrictions'],
      tags: ['license', 'royalty', 'technology', 'rights']
    },

    // Professional Services
    {
      id: '15',
      name: 'Consulting Agreement',
      description: 'Professional consulting contract for expert advisory services',
      category: 'Professional Services',
      estimatedTime: '4 minutes',
      popularity: 'Most Popular',
      icon: '📊',
      features: ['Hourly/project rates', 'Deliverables', 'Confidentiality'],
      tags: ['consulting', 'advisory', 'expert', 'professional']
    },
    {
      id: '16',
      name: 'Software Development',
      description: 'Custom software development project agreement',
      category: 'Professional Services',
      estimatedTime: '6 minutes',
      popularity: 'Popular',
      icon: '💻',
      features: ['Project milestones', 'Code ownership', 'Testing requirements'],
      tags: ['software', 'development', 'coding', 'technology']
    },
    {
      id: '17',
      name: 'Marketing Services',
      description: 'Digital marketing and advertising service contract',
      category: 'Professional Services',
      estimatedTime: '5 minutes',
      popularity: 'Popular',
      icon: '📱',
      features: ['Campaign objectives', 'Performance metrics', 'Creative approval'],
      tags: ['marketing', 'advertising', 'digital', 'campaign']
    },
    {
      id: '18',
      name: 'Legal Services Retainer',
      description: 'Ongoing legal representation and advisory services',
      category: 'Professional Services',
      estimatedTime: '5 minutes',
      popularity: 'Common',
      icon: '⚖️',
      features: ['Retainer structure', 'Scope of services', 'Billing arrangements'],
      tags: ['legal', 'lawyer', 'retainer', 'advisory']
    },

    // Real Estate
    {
      id: '19',
      name: 'Commercial Lease',
      description: 'Office or retail space rental agreement',
      category: 'Real Estate',
      estimatedTime: '8 minutes',
      popularity: 'Popular',
      icon: '🏢',
      features: ['Rent escalation', 'Maintenance responsibilities', 'Renewal options'],
      tags: ['lease', 'commercial', 'office', 'retail']
    },
    {
      id: '20',
      name: 'Residential Lease',
      description: 'Apartment or house rental agreement for tenants',
      category: 'Real Estate',
      estimatedTime: '6 minutes',
      popularity: 'Popular',
      icon: '🏠',
      features: ['Security deposit', 'Pet policies', 'Maintenance terms'],
      tags: ['residential', 'apartment', 'house', 'tenant']
    },
    {
      id: '21',
      name: 'Property Management',
      description: 'Agreement for professional property management services',
      category: 'Real Estate',
      estimatedTime: '7 minutes',
      popularity: 'Common',
      icon: '🔑',
      features: ['Management fees', 'Tenant relations', 'Maintenance coordination'],
      tags: ['property', 'management', 'landlord', 'maintenance']
    },

    // Financial
    {
      id: '22',
      name: 'Loan Agreement',
      description: 'Personal or business loan terms and repayment schedule',
      category: 'Financial',
      estimatedTime: '6 minutes',
      popularity: 'Common',
      icon: '💰',
      features: ['Interest rates', 'Repayment schedule', 'Default provisions'],
      tags: ['loan', 'finance', 'interest', 'repayment']
    },
    {
      id: '23',
      name: 'Investment Agreement',
      description: 'Terms for equity investment in startups or businesses',
      category: 'Financial',
      estimatedTime: '8 minutes',
      popularity: 'Growing',
      icon: '📈',
      features: ['Equity percentage', 'Valuation terms', 'Board representation'],
      tags: ['investment', 'equity', 'startup', 'funding']
    },
    {
      id: '24',
      name: 'Promissory Note',
      description: 'Simple debt instrument for money lending',
      category: 'Financial',
      estimatedTime: '3 minutes',
      popularity: 'Common',
      icon: '📝',
      features: ['Principal amount', 'Interest calculation', 'Maturity date'],
      tags: ['promissory', 'note', 'debt', 'lending']
    },

    // Creative & Media
    {
      id: '25',
      name: 'Photography Contract',
      description: 'Professional photography services for events or projects',
      category: 'Creative & Media',
      estimatedTime: '5 minutes',
      popularity: 'Popular',
      icon: '📸',
      features: ['Usage rights', 'Delivery timeline', 'Editing specifications'],
      tags: ['photography', 'creative', 'event', 'media']
    },
    {
      id: '26',
      name: 'Video Production',
      description: 'Video content creation and production services',
      category: 'Creative & Media',
      estimatedTime: '6 minutes',
      popularity: 'Growing',
      icon: '🎬',
      features: ['Production timeline', 'Revision rounds', 'Distribution rights'],
      tags: ['video', 'production', 'content', 'media']
    },
    {
      id: '27',
      name: 'Music Licensing',
      description: 'License music for commercial use in projects',
      category: 'Creative & Media',
      estimatedTime: '4 minutes',
      popularity: 'Common',
      icon: '🎵',
      features: ['Usage scope', 'Territory rights', 'Royalty terms'],
      tags: ['music', 'licensing', 'royalty', 'commercial']
    },

    // E-commerce
    {
      id: '28',
      name: 'Terms of Service',
      description: 'Website and app terms of service for users',
      category: 'E-commerce',
      estimatedTime: '5 minutes',
      popularity: 'Most Popular',
      icon: '📱',
      features: ['User obligations', 'Liability limitations', 'Dispute resolution'],
      tags: ['terms', 'website', 'app', 'users']
    },
    {
      id: '29',
      name: 'Privacy Policy',
      description: 'GDPR and CCPA compliant privacy policy',
      category: 'E-commerce',
      estimatedTime: '6 minutes',
      popularity: 'Most Popular',
      icon: '🔐',
      features: ['Data collection', 'Cookie policies', 'User rights'],
      tags: ['privacy', 'GDPR', 'CCPA', 'data']
    },
    {
      id: '30',
      name: 'Affiliate Agreement',
      description: 'Partnership terms for affiliate marketing programs',
      category: 'E-commerce',
      estimatedTime: '5 minutes',
      popularity: 'Popular',
      icon: '🤝',
      features: ['Commission structure', 'Marketing guidelines', 'Payment terms'],
      tags: ['affiliate', 'marketing', 'commission', 'partnership']
    }
  ];

  const categories = [
    'All', 
    'Legal Protection', 
    'Employment', 
    'Business Operations', 
    'Professional Services',
    'Real Estate',
    'Financial',
    'Creative & Media',
    'E-commerce'
  ];

  // Filter and search logic
  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'All' || template.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Sort templates
  const sortedTemplates = [...filteredTemplates].sort((a, b) => {
    switch (sortBy) {
      case 'popularity':
        const popularityOrder = { 'Most Popular': 0, 'Popular': 1, 'Growing': 2, 'Common': 3 };
        return (popularityOrder[a.popularity as keyof typeof popularityOrder] || 4) - 
               (popularityOrder[b.popularity as keyof typeof popularityOrder] || 4);
      case 'time':
        return parseInt(a.estimatedTime) - parseInt(b.estimatedTime);
      case 'alphabetical':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  const getPopularityColor = (popularity: string) => {
    switch (popularity) {
      case 'Most Popular':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'Popular':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-dark-100 dark:to-dark-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-6">
              Contract Templates
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              Choose from our library of {templates.length} professionally crafted contract templates. 
              Each template is legally reviewed and ready to customize for your needs.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search templates by name, description, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-white dark:bg-dark-200 border border-gray-300 dark:border-dark-300 rounded-2xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent shadow-lg"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filters and Controls */}
      <section className="py-6 bg-white dark:bg-dark-100 border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Results and Sort */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
            <div className="mb-4 lg:mb-0">
              <p className="text-gray-600 dark:text-gray-300">
                Showing <span className="font-semibold text-gray-900 dark:text-white">{sortedTemplates.length}</span> of {templates.length} templates
                {searchTerm && (
                  <span className="ml-2">
                    for "<span className="font-medium">{searchTerm}</span>"
                  </span>
                )}
              </p>
            </div>
            
            {/* Sort Options */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-300">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white dark:bg-dark-200 border border-gray-300 dark:border-dark-300 rounded-lg px-3 py-1 text-sm text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="popularity">Popularity</option>
                  <option value="time">Time to Create</option>
                  <option value="alphabetical">A-Z</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => {
              const count = category === 'All' ? templates.length : templates.filter(t => t.category === category).length;
              return (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full font-medium transition-all duration-200 text-sm ${
                    selectedCategory === category
                      ? 'bg-primary-600 text-white shadow-lg'
                      : 'bg-gray-100 dark:bg-dark-200 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-dark-300'
                  }`}
                >
                  {category} ({count})
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Templates Grid */}
      <section className="py-12 bg-white dark:bg-dark-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {sortedTemplates.length === 0 ? (
            /* No Results State */
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                No templates found
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Try adjusting your search terms or category filters
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('All');
                }}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                Clear Filters
              </button>
            </motion.div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {sortedTemplates.map((template, index) => (
                <motion.div
                  key={template.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.05 }}
                  className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark-200 dark:to-dark-300 rounded-2xl p-6 hover:shadow-xl hover:scale-105 transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50 group"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="text-3xl">{template.icon}</div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPopularityColor(template.popularity)}`}>
                        {template.popularity}
                      </span>
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <Clock className="w-3 h-3 mr-1" />
                        {template.estimatedTime}
                      </div>
                    </div>
                  </div>
                  
                  {/* Title and Description */}
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {template.name}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 leading-relaxed line-clamp-2">
                    {template.description}
                  </p>
                  
                  {/* Category Badge */}
                  <div className="mb-4">
                    <span className="inline-flex items-center text-xs bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 px-2 py-1 rounded-full">
                      {template.category}
                    </span>
                  </div>
                  
                  {/* Key Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Key Features:
                    </h4>
                    <ul className="space-y-1">
                      {template.features.slice(0, 3).map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-xs text-gray-600 dark:text-gray-300 flex items-start">
                          <div className="w-1 h-1 bg-primary-500 rounded-full mr-2 mt-1.5 flex-shrink-0" />
                          <span className="line-clamp-1">{feature}</span>
                        </li>
                      ))}
                      {template.features.length > 3 && (
                        <li className="text-xs text-gray-500 dark:text-gray-400 italic">
                          +{template.features.length - 3} more features
                        </li>
                      )}
                    </ul>
                  </div>
                  
                  {/* Tags */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-1">
                      {template.tags.slice(0, 3).map((tag, tagIndex) => (
                        <span key={tagIndex} className="text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">
                          #{tag}
                        </span>
                      ))}
                      {template.tags.length > 3 && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">+{template.tags.length - 3}</span>
                      )}
                    </div>
                  </div>
                  
                  {/* CTA Button */}
                  <Link
                    to="/create"
                    className="w-full inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-xl font-medium transition-all duration-200 group-hover:shadow-lg"
                  >
                    <span>Use This Template</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {templates.length}+
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Contract Templates
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {categories.length - 1}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Categories
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                2-8
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Minutes to Create
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                100%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Legally Reviewed
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-50 dark:bg-dark-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Custom Contract */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <div className="text-5xl mb-4">🤖</div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Need a Custom Template?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                Can't find the perfect template? Our AI can create custom contracts tailored to your specific requirements in minutes.
              </p>
              <Link
                to="/create"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl hover:from-primary-600 hover:to-secondary-600 transition-all duration-200 shadow-lg hover:shadow-xl font-semibold"
              >
                <span>Create Custom Contract</span>
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </motion.div>

            {/* Right Column - Browse All */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center lg:text-left"
            >
              <div className="text-5xl mb-4">📚</div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Explore All Templates
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                Browse our complete library of {templates.length} professional contract templates across {categories.length - 1} categories.
              </p>
              <div className="flex flex-wrap gap-2 mb-6">
                {categories.slice(1, 6).map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className="px-3 py-1 bg-white dark:bg-dark-300 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors duration-200"
                  >
                    {category}
                  </button>
                ))}
              </div>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('All');
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
                className="inline-flex items-center px-6 py-3 bg-white dark:bg-dark-300 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-dark-400 transition-all duration-200 shadow-md hover:shadow-lg font-semibold"
              >
                <span>Browse All Templates</span>
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TemplatesPage;
