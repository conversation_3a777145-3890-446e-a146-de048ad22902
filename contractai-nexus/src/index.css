@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:wght@100..800&family=Lexend:wght@100..900&display=swap');

@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-white dark:bg-dark-100 text-gray-900 dark:text-gray-100 font-sans antialiased;
  }

  ::selection {
    @apply bg-primary-500 bg-opacity-20 dark:bg-primary-500 dark:bg-opacity-30;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.3;
  }
}

@layer components {
  .glass-panel {
    @apply bg-white bg-opacity-70 dark:bg-dark-200 dark:bg-opacity-70 backdrop-blur-lg rounded-2xl border border-white border-opacity-20 dark:border-dark-300 dark:border-opacity-50;
  }
  
  .glass-panel-hover {
    @apply glass-panel transition-all duration-300 hover:shadow-glow dark:hover:shadow-glow;
  }

  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-xl shadow-md hover:shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-white dark:bg-dark-200 hover:bg-gray-50 dark:hover:bg-dark-300 text-gray-900 dark:text-white font-medium rounded-xl border border-gray-200 dark:border-dark-400 shadow-sm hover:shadow-md transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-dark-500 focus:ring-opacity-50;
  }

  .input-field {
    @apply w-full px-4 py-3 bg-white dark:bg-dark-200 border border-gray-200 dark:border-dark-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-transparent transition-all duration-300;
  }

  .card {
    @apply bg-white dark:bg-dark-200 rounded-2xl shadow-md dark:shadow-md overflow-hidden;
  }

  .card-hover {
    @apply card transition-all duration-300 hover:shadow-xl hover:scale-[1.02] dark:hover:shadow-dark-300;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-500 to-secondary-500 animate-gradient bg-[length:200%_auto];
  }

  .neu-button {
    @apply px-6 py-3 bg-gray-100 dark:bg-dark-200 rounded-xl shadow-neu-light dark:shadow-neu-dark transition-all duration-300 hover:shadow-none active:shadow-inner;
  }
}
