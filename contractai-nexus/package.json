{"name": "contractai-nexus", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "lucide-react": "^0.536.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@types/three": "^0.178.1", "autoprefixer": "^10.4.21", "framer-motion": "^12.23.12", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "three": "^0.178.0"}}